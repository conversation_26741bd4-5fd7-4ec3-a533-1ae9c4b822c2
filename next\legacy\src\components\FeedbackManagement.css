/* Feedback Management Styles */
.feedback-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
}

.feedback-header h2 {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 2rem;
  color: #966f33;
  margin: 0;
}

.feedback-actions {
  display: flex;
  gap: 10px;
}

.export-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 0; /* Sharp rectangular corners */
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.csv-btn {
  background-color: #2563eb;
  color: white;
}

.csv-btn:hover:not(:disabled) {
  background-color: #1d4ed8;
}

.json-btn {
  background-color: #059669;
  color: white;
}

.json-btn:hover:not(:disabled) {
  background-color: #047857;
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Statistics Section */
.feedback-stats {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(150, 111, 51, 0.2);
  padding: 25px;
  margin-bottom: 30px;
  border-radius: 0; /* Sharp rectangular corners */
}

.feedback-stats h3 {
  font-family: 'Battambang', sans-serif;
  font-size: 1.5rem;
  color: #966f33;
  margin: 0 0 20px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.stat-card {
  background: rgba(150, 111, 51, 0.05);
  border: 1px solid rgba(150, 111, 51, 0.2);
  padding: 20px;
  text-align: center;
  border-radius: 0; /* Sharp rectangular corners */
}

.stat-card h4 {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.9rem;
  color: #666;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 2rem;
  color: #966f33;
  font-weight: bold;
  margin: 0;
}

.breakdown-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.breakdown-item h4 {
  font-family: 'Battambang', sans-serif;
  font-size: 1.1rem;
  color: #966f33;
  margin: 0 0 15px 0;
}

.breakdown-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.breakdown-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(150, 111, 51, 0.05);
  border-left: 3px solid #966f33;
}

.breakdown-row span:first-child {
  font-family: 'Source Sans Pro', sans-serif;
  color: #3e3e3e;
}

.breakdown-row span:last-child {
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  color: #966f33;
}

/* Filters Section */
.feedback-filters {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(150, 111, 51, 0.2);
  padding: 25px;
  margin-bottom: 30px;
  border-radius: 0; /* Sharp rectangular corners */
}

.feedback-filters h3 {
  font-family: 'Battambang', sans-serif;
  font-size: 1.3rem;
  color: #966f33;
  margin: 0 0 20px 0;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  color: #3e3e3e;
  font-size: 0.9rem;
}

.filter-input,
.filter-select {
  padding: 10px 12px;
  border: 2px solid rgba(150, 111, 51, 0.3);
  border-radius: 0; /* Sharp rectangular corners */
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.95rem;
  background: white;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #966f33;
}

.clear-filters-btn {
  padding: 10px 20px;
  background-color: #6b7280;
  color: white;
  border: none;
  border-radius: 0; /* Sharp rectangular corners */
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.clear-filters-btn:hover {
  background-color: #4b5563;
}

/* Feedback List */
.feedback-list {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(150, 111, 51, 0.2);
  padding: 25px;
  border-radius: 0; /* Sharp rectangular corners */
}

.feedback-list h3 {
  font-family: 'Battambang', sans-serif;
  font-size: 1.3rem;
  color: #966f33;
  margin: 0 0 25px 0;
}

.no-feedback {
  text-align: center;
  padding: 40px;
  color: #666;
  font-family: 'Source Sans Pro', sans-serif;
  font-style: italic;
}

.feedback-table {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feedback-item {
  border: 1px solid rgba(150, 111, 51, 0.2);
  border-radius: 0; /* Sharp rectangular corners */
  overflow: hidden;
}

.feedback-item-header {
  background: rgba(150, 111, 51, 0.1);
  padding: 15px 20px;
  border-bottom: 1px solid rgba(150, 111, 51, 0.2);
}

.feedback-meta {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.feedback-id {
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  color: #966f33;
}

.feedback-date {
  font-family: 'Source Sans Pro', sans-serif;
  color: #666;
  font-size: 0.9rem;
}

.feedback-user {
  font-family: 'Source Sans Pro', sans-serif;
  color: #3e3e3e;
  font-weight: 500;
}

.feedback-content {
  padding: 20px;
}

.feedback-choices {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(150, 111, 51, 0.1);
}

.choice-item {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.95rem;
  color: #3e3e3e;
}

.choice-item strong {
  color: #966f33;
}

.feedback-answers {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.answer-item strong {
  font-family: 'Battambang', sans-serif;
  color: #966f33;
  display: block;
  margin-bottom: 8px;
}

.answer-item p {
  font-family: 'Source Sans Pro', sans-serif;
  color: #3e3e3e;
  line-height: 1.6;
  margin: 0;
  padding: 12px;
  background: rgba(150, 111, 51, 0.05);
  border-left: 3px solid #966f33;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(150, 111, 51, 0.2);
}

.pagination-btn {
  padding: 10px 20px;
  background-color: #966f33;
  color: white;
  border: none;
  border-radius: 0; /* Sharp rectangular corners */
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #7a5a2a;
}

.pagination-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.pagination-info {
  font-family: 'Source Sans Pro', sans-serif;
  color: #666;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .feedback-management {
    padding: 15px;
  }

  .feedback-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .feedback-actions {
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .filters-grid {
    grid-template-columns: 1fr;
  }

  .breakdown-section {
    grid-template-columns: 1fr;
  }

  .feedback-choices {
    grid-template-columns: 1fr;
  }

  .feedback-meta {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .pagination {
    flex-direction: column;
    gap: 10px;
  }
}
