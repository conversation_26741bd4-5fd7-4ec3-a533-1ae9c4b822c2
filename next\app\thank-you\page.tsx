import type { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { seoData } from "../../legacy/src/utils/seoData";
import { createMetadataWithIcons } from "../../legacy/src/utils/metadata";
import ThankYouClient from "../_clients/ThankYouClient";

export const metadata: Metadata = createMetadataWithIcons({
  title: seoData.thankYou.title,
  description: seoData.thankYou.description,
  robots: seoData.thankYou.noIndex ? { index: false, follow: false } : undefined,
  openGraph: {
    title: seoData.thankYou.title,
    description: seoData.thankYou.description,
    url: seoData.thankYou.url,
  },
  alternates: {
    canonical: seoData.thankYou.url,
  },
});

export default function Page() {
  return (
    <Suspense fallback={null}>
      <ThankYouClient />
    </Suspense>
  );
}

