.feature-container {
    display: flex;
    height: fit-content;
    flex-direction: column;
    width: 100vw;
    position: relative;
    margin-bottom: 400px;
    margin-top: 150px;




    background-color: #F7F3ED;
    justify-content: center;


}

.feature-text-container h1 {

    font-weight: 300;
    font-family: 'Nyght Serif', serif;
    text-align: center;
    height: fit-content;
    font-size: clamp(2rem, 3vw + 1rem, 6.5rem);
    color: #0D1A2E;
    padding-bottom: 1rem;
}

.feature-text-container p {
    font-weight: 300;
    font-family: 'Nyght Serif', serif;
    text-align: center;
    height: fit-content;
    font-size: clamp(1rem, 2vw + 1rem, 2rem);
    color: #0D1A2E;
}

.feature-content {
    gap: clamp(130px, 20vw + 1rem, 290px);
    display: flex;
    flex-direction: column;
    margin-right: clamp(1rem, 3vw + 1rem, 5rem);
    margin-left: clamp(1rem, 3vw + 1rem, 5rem);
    margin-top: clamp(5rem, 4vw + 5rem, 20rem);
    padding-bottom: 200px;
    position: relative;
    padding-top: 100px;
}

.feature-content-item {
    height: fit-content;
    display: flex;
    flex-direction: row;
    gap: clamp(50px, 10vw + 1rem, 250px);
    width: 100%;
}

.feature-content-item-left1 {
    background-image: url('../../../../public/feature1.png');
    background-size: cover;
    background-position: center;
    aspect-ratio: 743/514;
    flex: 1;
}

.feature-content-item-left2 {
    background-image: url('../../../../public/feature2.png');
    background-size: cover;
    background-position: center;
    aspect-ratio: 743/514;
    flex: 1;
}

.feature-content-item-left3 {
    background-image: url('../../../../public/feature3.png');
    background-size: cover;
    background-position: center;
    aspect-ratio: 743/514;
    flex: 1;
}

.feature-content-item-left4 {
    background-image: url('../../../../public/feature4.png');
    background-size: cover;
    background-position: center;
    aspect-ratio: 743/514;
    flex: 1;
}

.feature-content-item-right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding-right: 10px;
    gap: 3rem;
    flex: 1;
}

.feature-content-item-right h2 {
    font-family: 'Nyght Serif', serif;
    font-weight: lighter;
    color: #3C1F13;
    line-height: 1.2;
    font-size: clamp(35px, 3vw + 1rem, 60px);
}

.feature-content-item-right li {
    font-size: clamp(22px, 2vw + 2rem, 24px);
    font-family: 'Arial';
    list-style-type: disc;
    margin-left: 30px;
    line-height: 2.2;
}

.timeline {
    position: relative;


    width: 3px;
    top: -10px;

    background-color: #7C5D00;
    height: 100%;
}

.timeline-container {
    position: absolute;
    left: 50%;
    width: 92px;
    height: 100%;



}

.compass {
    position: absolute;



    background-image: url('../../../../public/compass.png');
    background-size: cover;
    aspect-ratio: 1/1;
    left: -42px;
    height: 89.78px;

    width: 90px;
    image-rendering: crisp-edges;

}

/* Refinement for 1025px-1220px range */
@media (min-width: 1025px) and (max-width: 1220px) {
    .feature-content-item-left1,
    .feature-content-item-left2,
    .feature-content-item-left3,
    .feature-content-item-left4 {
        max-width: clamp(380px, 40vw, 480px);
    }

    .feature-content-item {
        gap: clamp(40px, 7vw, 100px);
        align-items: center;
    }
}

/* Simple fix for 1220px-1440px range - just increase gap to prevent overlap */
@media (min-width: 1221px) and (max-width: 1440px) {
    .feature-content-item {
        gap: clamp(80px, 10vw, 150px);
    }
}

@media (max-width: 1024px) {
    .timeline-container {
        left: 0;
        width: 44px;
        height: 99%;
    }

    .feature-content-item {
        flex-direction: column;
        padding-left: 60px;
        align-items: flex-start;
    }

    .feature-content-item-left1,
    .feature-content-item-left2,
    .feature-content-item-left3,
    .feature-content-item-left4 {
        width: clamp(300px, 80vw, 600px);
        max-width: none;
        order: 1;
        margin-bottom: 2rem;
    }

    .feature-content-item-right {
        order: 2;
        max-width: none;
        align-items: flex-start;
        text-align: left;
    }

    .compass {
        height: 44px;
        width: 44px;
    }

    .feature-container {
        margin-bottom: 80px;
    }

    .feature-content-item-right li {
        line-height: 1.5;
        font-size: 17px;
    }

    .feature-content {
        margin-top: 0;
        gap: 200px;
    }
}