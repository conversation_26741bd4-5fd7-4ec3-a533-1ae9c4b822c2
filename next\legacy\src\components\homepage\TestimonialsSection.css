@font-face {
    font-family: 'Nyght Serif';
    src: url('/fonts/nyght/NyghtSerif-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Nyght Serif';
    src: url('/fonts/nyght/NyghtSerif-LightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

.testimonials-section {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    padding: 0;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    margin-bottom: 200px;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    bottom: 0;
    background-image: url('/testimonialbg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
   
    transform: scale(1.1);
    z-index: 0;
}

.testimonials-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100vh;
    bottom: 0;
    
    z-index: 1;
}

.testimonials-content {
    max-width: clamp(800px, 90vw, 1400px);
    margin: 0 auto;
    padding: 0 clamp(1rem, 5vw, 4rem);
    display: flex;
    align-items: start;
    position: relative;
    z-index: 2;
    width: 100%;
}

.testimonials-left {
    flex: 0 0 clamp(50%, 35vw, 40%);
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: clamp(2rem, 6vh, 4rem);
    justify-content: center;
    text-align: center;
}

.testimonials-title {
    font-family: "Nyght Serif", serif;
    font-weight: lighter;
    font-size: clamp(2.5rem, 6vw, 5rem);
    line-height: clamp(1.05, 1.1, 1.15);
    text-align: left;
    color: #ffffff;
    margin: 0;
}

.testimonials-button {
    font-family: 'Arial';
    font-weight: light;
    font-size: 20px;
    background: transparent;
    color: #ffffff;
    
    
    
    text-decoration: underline;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    text-align: center;
}

.testimonials-button:hover {
    
    color: #cfcfcf;
    transform: translateY(-1px);
    
}

.testimonials-right {
    flex: 0 0 clamp(55%, 60vw, 65%);
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: clamp(0.5rem, 0.25vw, 2rem);
}

.testimonials-stack {
    position: relative;
    cursor: pointer;
    perspective: 1000px;
}

.testimonial-card {
    width: clamp(300px, 35vw, 450px);
    height: clamp(500px, 65vh, 750px);
    background: #ffffff;
    border-radius: clamp(5px, 2vw, 10px);
    box-shadow: 0 clamp(15px, 3vw, 30px) clamp(30px, 6vw, 60px) rgba(0, 0, 0, 0.4);
    overflow: hidden;
    position: relative;
    transform-style: preserve-3d;
    border: clamp(4px, 1vw, 10px) solid #ffffff;
    display: flex;
    flex-direction: column;
}

.stack-card {
    position: absolute;
    top: 0;
    left: 0;
    background: #ffffff;
    border: 8px solid #f5f5f5;
}

.stack-1 {
    z-index: 1;
}

.stack-2 {
    z-index: 0;
}

.main-card {
    z-index: 3;
    position: relative;
    height: 100%;
    
}

.testimonial-image {
    width: 100%;
    height: clamp(220px, 30vh, 300px);
    overflow: hidden;
    position: relative;
    
    flex-shrink: 0;
}

.testimonial-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.testimonial-content {
    display: flex;
    flex-direction: column;
    gap: clamp(8px, 1.5vh, 15px);
    flex: 1;
    box-sizing: border-box;
    
    background: #ffffff;
    padding: clamp(15px, 2vw, 25px);
    min-height: 0;
}

.testimonial-name {
    font-family: 'Nyght Serif',serif;
    font-weight: 300;
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    color: #000000;
    margin: 0.25rem;
    text-align: center;
    line-height: clamp(1.05, 1.1, 1.15);
}

.testimonial-profession {
    font-family: 'Arial';
    font-weight: 400;
    font-size: clamp(0.8rem, 1.2vw, 1rem);
    color: #888888;
    margin: 0 0 clamp(15px, 2vh, 25px) 0;
    text-align: center;
    line-height: clamp(1.3, 1.4, 1.5);
    max-width: clamp(250px, 80%, 350px);
    margin-left: auto;
    margin-right: auto;
}

.testimonial-quote-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 0 clamp(15px, 2vh, 25px) 0;
}

.testimonial-quote {
    font-family: "Nyght Serif", serif;
    font-weight: 300;
    font-style: italic;
    font-size: clamp(0.9rem, 1.3vw, 1.1rem);
    color: #2c2c2c;
    margin: 0;
    text-align: center;
    line-height: clamp(1.4, 1.5, 1.6);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 clamp(5px, 1vw, 10px);
    overflow-y: auto;
    width: 100%;
}

.testimonial-dots {
    display: none;
    justify-content: center;
    gap: 10px;
    margin-top: auto;
    padding-bottom: 5px;
}

.testimonial-dots span {
    width: 8px;
    height: 8px;
    background: #d4af37;
    border-radius: 50%;
    opacity: 0.7;
}

.testimonial-dots span.active {
    opacity: 1;
    transform: scale(1.1);
}

/* Mobile tap instruction */
.mobile-tap-instruction {
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: clamp(0.5rem, 2vw, 1rem);
    margin-top: clamp(2rem, 5vh, 3rem);
    width: 100%;
}

.mobile-tap-instruction p {
    font-family: 'Arial';
    font-size: clamp(0.9rem, 3vw, 1rem);
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    text-align: center;
    font-weight: 300;
}

.person-indicators {
    display: flex;
    gap: 8px;
    align-items: center;
}

.person-dot {
    width: 10px;
    height: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.person-dot.active {
    background: #ffffff;
    
}

/* Hide join button on mobile */
.desktop-only {
    display: block;
}

/* Pinning wrapper styles */
.pinned-testimonials-wrapper {
    height: 500vh; /* 5x viewport height for better scroll distribution */
    position: relative;
}

.pinned-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Scroll progress indicator */
.scroll-progress-indicator {
    position: absolute;
    bottom: clamp(2rem, 5vh, 4rem);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    width: 100%;
    max-width: 400px;
}

.progress-bar {
    width: clamp(200px, 40vw, 300px);
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1px;
    transform-origin: left;
}

.scroll-hint {
    font-family: 'Arial';
    font-size: clamp(0.8rem, 2.5vw, 0.9rem);
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    text-align: center;
    font-weight: 300;
}

/* Container-based responsive design */
@container (max-width: 1000px) {
    .testimonials-content {
        gap: min(6rem, 10%);
        padding: 0 min(40px, 4%);
    }

    .testimonials-title {
        font-size: clamp(3rem, 5vw, 3.5rem);
    }

    .testimonials-right {
        padding-left: 20px;
    }

    .testimonial-card {
        width: 380px;
        height: 520px;
    }

    .testimonial-image {
        height: 280px;
        margin: 12px 12px 0 12px;
        width: calc(100% - 24px);
    }

    .testimonial-content {
        height: 240px;
        padding: 20px 25px 25px 25px;
    }
}

@container (max-width: 800px) {
    .testimonials-section {
        min-height: auto;
        padding: 60px 0;
    }

    .testimonials-section::before {
        background-attachment: scroll;
    }

    .testimonials-content {
        flex-direction: column;
        gap: 60px;
        text-align: center;
    }

    .testimonials-left {
        flex: none;
        align-items: center;
        gap: 40px;
        text-align: center;
        justify-content: center;
    }

    .testimonials-right {
        flex: none;
        padding-left: 0;
    }

    .testimonials-title {
        font-size: clamp(2.5rem, 6vw, 3.5rem);
        text-align: center;
    }

    .testimonial-card {
        width: 340px;
        height: 460px;
    }

    .testimonial-image {
        height: 250px;
        margin: 10px 10px 0 10px;
        width: calc(100% - 20px);
    }

    .testimonial-content {
        padding: 20px 18px 25px 18px;
        height: 210px;
    }

    .testimonial-name {
        font-size: 1.6rem;
    }

    .testimonial-profession {
        font-size: 0.85rem;
    }

    .testimonial-quote {
        font-size: 1rem;
    }
}

@container (max-width: 600px) {
    .testimonials-section {
        padding: 50px 0;
    }

    .testimonials-content {
        padding: 0 min(30px, 5%);
        gap: 50px;
    }

    .testimonials-left {
        gap: 30px;
        text-align: center;
        justify-content: center;
    }

    .testimonials-title {
        font-size: 35px;
        text-align: left;
    }

    .testimonial-card {
        width: 300px;
        height: 400px;
    }

    .testimonial-image {
        height: 220px;
        margin: 8px 8px 0 8px;
        width: calc(100% - 16px);
    }

    .testimonial-content {
        padding: 15px 12px 20px 12px;
        height: 180px;
        gap: 10px;
    }

    .testimonial-name {
        font-size: 1.4rem;
    }

    .testimonial-profession {
        font-size: 0.8rem;
    }

    .testimonial-quote {
        font-size: 0.95rem;
        margin: 10px 0;
    }

    .testimonials-button {
        font-size: 0.9rem;
        padding: 10px 20px;
    }
}

@container (max-width: 400px) {
    .testimonials-content {
        padding: 0 min(20px, 4%);
        gap: 40px;
    }

    .testimonials-title {
        font-size: clamp(1.8rem, 4.5vw, 2.2rem);
    }

    .testimonial-card {
        width: 280px;
        height: 380px;
    }

    .testimonial-image {
        height: 200px;
        margin: 6px 6px 0 6px;
        width: calc(100% - 12px);
    }

    .testimonial-content {
        height: 180px;
        padding: 12px 10px 15px 10px;
        gap: 8px;
    }

    .testimonial-name {
        font-size: 1.3rem;
    }

    .testimonial-quote {
        font-size: 0.9rem;
    }
}

/* Mobile layout adjustments */
@media (max-width: 768px) {
    .testimonials-section::before {
        background-attachment: scroll;
    }

    .testimonials-content {
        flex-direction: column;
        text-align: center;
        gap: clamp(1rem, 10vh, 3rem);
    }

    .testimonials-left {
        flex: none;
        align-items: center;
        width: 100%;
        text-align: center;
    }

    .testimonials-right {
        flex: none;
        padding-left: 0;
        width: 100%;
        justify-content: center;
    }

    .testimonials-title {
        text-align: left;
        width: clamp(20rem, 5vw, 30rem);
        font-size:35px;
        font-weight:100;
    }

    .desktop-only {
        display: none;
    }

    .mobile-tap-instruction {
        display: flex;
    }

    .scroll-progress-indicator {
        display: none;
    }

    .pinned-testimonials-wrapper {
        height: 400vh; /* Shorter on mobile but still adequate */
    }

    .testimonial-card {
        height: clamp(450px, 60vh, 600px);
        width: clamp(280px, 85vw, 380px);
    }

    .testimonial-image {
        height: clamp(180px, 25vh, 220px);
    }

    .testimonial-quote {
        font-size: clamp(0.85rem, 3.5vw, 1rem);
        line-height: 1.4;
    }
}

/* Performance optimizations */
.testimonials-section * {
    will-change: transform, opacity;
}

.testimonial-card {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.testimonials-stack {
    transform: translateZ(0);
    backface-visibility: hidden;
}