@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");

.about {
  min-height: 100vh;
  background: linear-gradient(135deg, #FDFBF4 0%, #FFF8E4 50%, #FDFBF4 100%);
  color: #26211D;
}

.about__container {
  margin: 0 auto;
}

.about__hero {
  padding: 64px 20px 24px;
  border-bottom: 1px solid #DFDBD7;
}

.about__hero-content {
  max-width: 1000px;
  margin: 0 auto;
  text-align: center;
}

.about__title {
  font-family: 'Merriweather', serif;
  font-size: 2.25rem;
  margin: 0 0 12px;
}

.about__subtitle {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1.05rem;
  line-height: 1.8;
  color: #4A4540;
}

.about__section {
  padding: 24px 20px;
}

.about__section + .about__section {
  border-top: 1px solid #DFDBD7;
}

.about__section-content {
  max-width: 900px;
  margin: 0 auto;
}

.about__section-content h2 {
  font-family: 'Merriweather', serif;
  margin: 0 0 12px;
}

.about__section-content p,
.about__section-content li {
  font-family: 'Source Sans Pro', sans-serif;
  color: #4A4540;
}

.about__section-content ul {
  padding-left: 16px;
}

.about__cta {
  padding: 28px 20px;
  border-top: 1px solid #DFDBD7;
  border-bottom: 1px solid #DFDBD7;
  text-align: center;
}

.about__cta h3 {
  font-family: 'Merriweather', serif;
  margin: 0 0 16px;
}

/* FAQ */
.about__faq {
  display: grid;
  gap: 12px;
}

.faq-item {
  border: 1px solid #DFDBD7;
  background: #fffdf6;
}

.faq-question {
  width: 100%;
  text-align: left;
  padding: 12px 14px;
  background: transparent;
  border: none;
  font-family: 'Merriweather', serif;
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-answer {
  padding: 0 14px 14px;
  font-family: 'Source Sans Pro', sans-serif;
  color: #4A4540;
}

.faq-icon {
  font-family: 'Source Sans Pro', sans-serif;
}

