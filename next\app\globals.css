@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #000000;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}


/* LawVriksh styles from Vite app */


@font-face {
  font-family: 'Nyght Serif';
  src: url('/fonts/nyght/NyghtSerif-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nyght Serif';
  src: url('/fonts/nyght/NyghtSerif-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nyght Serif';
  src: url('/fonts/nyght/NyghtSerif-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Nyght Serif';
  src: url('/fonts/nyght/NyghtSerif-LightItalic.woff2') format('woff2');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Nyght Serif';
  src: url('/fonts/nyght/NyghtSerif-RegularItalic.woff2') format('woff2');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

html { scroll-behavior: smooth; }
body { overflow-x: hidden; background-color: #F7F3ED; }
