{"legacy\\src\\components\\Homepage.tsx -> ./Footer": {"id": "legacy\\src\\components\\Homepage.tsx -> ./Footer", "files": ["static/css/_app-pages-browser_legacy_src_components_Footer_tsx.css", "static/chunks/_app-pages-browser_legacy_src_components_Footer_tsx.js"]}, "legacy\\src\\components\\Homepage.tsx -> ./MarketGrowth": {"id": "legacy\\src\\components\\Homepage.tsx -> ./MarketGrowth", "files": ["static/chunks/_app-pages-browser_legacy_src_components_MarketGrowth_tsx.js"]}, "legacy\\src\\components\\Homepage.tsx -> ./WaitlistPopup": {"id": "legacy\\src\\components\\Homepage.tsx -> ./WaitlistPopup", "files": ["static/css/_app-pages-browser_legacy_src_components_WaitlistPopup_tsx.css", "static/chunks/_app-pages-browser_legacy_src_components_WaitlistPopup_tsx.js"]}, "node_modules\\next\\dist\\client\\dev\\hot-reloader\\app\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "node_modules\\next\\dist\\client\\dev\\hot-reloader\\app\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}}