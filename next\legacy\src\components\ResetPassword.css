/* Reset Password Component Styles */

.reset-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f9fafb;
  padding: 3rem 1rem;
}

.reset-password-card {
  max-width: 28rem;
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 2rem;
}

.reset-password-header {
  text-align: center;
  margin-bottom: 2rem;
}

.reset-password-icon {
  margin: 0 auto 1.5rem;
  height: 3rem;
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #dbeafe;
}

.reset-password-icon svg {
  height: 1.5rem;
  width: 1.5rem;
  color: #2563eb;
}

.reset-password-icon.error {
  background-color: #fecaca;
}

.reset-password-icon.error svg {
  color: #dc2626;
}

.reset-password-icon.success {
  background-color: #dcfce7;
}

.reset-password-icon.success svg {
  color: #16a34a;
}

.reset-password-title {
  font-family: 'Baskerville Old Face', serif;
  font-size: 1.875rem;
  font-weight: 800;
  color: #111827;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.reset-password-subtitle {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.reset-password-form {
  margin-top: 2rem;
}

.reset-password-field {
  margin-bottom: 1.5rem;
}

.reset-password-label {
  font-family: 'Source Sans Pro', sans-serif;
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.reset-password-input-container {
  position: relative;
}

.reset-password-input {
  font-family: 'Source Sans Pro', sans-serif;
  appearance: none;
  position: relative;
  display: block;
  width: 100%;
  padding: 0.75rem;
  padding-right: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  color: #111827;
  font-size: 0.875rem;
  background-color: white;
  transition: all 0.15s ease-in-out;
  box-sizing: border-box;
}

.reset-password-input::placeholder {
  color: #9ca3af;
}

.reset-password-input:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.reset-password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reset-password-toggle:hover {
  color: #374151;
}

.reset-password-toggle svg {
  height: 1.25rem;
  width: 1.25rem;
}

.reset-password-strength {
  margin-top: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
}

.reset-password-strength h4 {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.reset-password-strength-bar {
  height: 0.25rem;
  background-color: #e5e7eb;
  border-radius: 2px;
  margin-bottom: 0.5rem;
  overflow: hidden;
}

.reset-password-strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.reset-password-strength-fill.weak {
  width: 25%;
  background-color: #ef4444;
}

.reset-password-strength-fill.fair {
  width: 50%;
  background-color: #f59e0b;
}

.reset-password-strength-fill.good {
  width: 75%;
  background-color: #10b981;
}

.reset-password-strength-fill.strong {
  width: 100%;
  background-color: #059669;
}

.reset-password-requirements {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.75rem;
  color: #6b7280;
}

.reset-password-requirements ul {
  margin: 0;
  padding-left: 1rem;
  list-style: none;
}

.reset-password-requirements li {
  margin-bottom: 0.25rem;
  position: relative;
}

.reset-password-requirements li::before {
  content: '•';
  position: absolute;
  left: -0.75rem;
  color: #d1d5db;
}

.reset-password-requirements li.met {
  color: #059669;
}

.reset-password-requirements li.met::before {
  content: '✓';
  color: #059669;
}

.reset-password-button {
  font-family: 'Merriweather', serif;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  color: white;
  background-color: #2563eb;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  min-height: 2.75rem;
}

.reset-password-button:hover:not(:disabled) {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.reset-password-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

.reset-password-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reset-password-links {
  text-align: center;
  margin-top: 1.5rem;
}

.reset-password-link {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2563eb;
  text-decoration: none;
  transition: color 0.15s ease-in-out;
}

.reset-password-link:hover {
  color: #1d4ed8;
  text-decoration: underline;
}

.reset-password-success-actions {
  text-align: center;
  margin-top: 2rem;
}

.reset-password-success-button {
  font-family: 'Merriweather', serif;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 6px;
  color: white;
  background-color: #2563eb;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  margin-bottom: 1rem;
}

.reset-password-success-button:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

/* Responsive Design */
@media (max-width: 640px) {
  .reset-password-container {
    padding: 1.5rem 1rem;
  }
  
  .reset-password-card {
    padding: 1.5rem;
  }
  
  .reset-password-title {
    font-size: 1.5rem;
  }
}
