@font-face {
    font-family: 'Nyght Serif';
    src: url('/src/fonts/nyght/NyghtSerif-Light.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

.why-lawvriksh-section {
    overflow: hidden;
    width: 100%;
    background-color: #F7F3ED;
    padding: clamp(3rem, 8vh, 6rem) 0;
    box-sizing: border-box;
    margin-bottom: clamp(3rem, 15vh + 1rem, 237px);
    margin-top: clamp(3rem, 15vh + 1rem, 237px);
}

.why-content {
    max-width: clamp(800px, 90vw, 1400px);
    margin: 0 auto;
    padding: 0 clamp(1rem, 5vw, 4rem);
    display: flex;
    gap: clamp(2rem, 8vw, 10rem);
    align-items: flex-start;
}

.why-left {
    flex: 0 0 clamp(30%, 35vw, 40%);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: clamp(2rem, 6vh, 4rem);
}

.why-title {
    font-family: "Nyght Serif", serif;
    font-weight: 300;
    font-size: clamp(2.5rem, 6vw, 5rem);
    line-height: clamp(1.05, 1.1, 1.15);
    color: #000000;
    margin: 0;
}

.why-button {
    font-family: 'Arial';
    font-weight: 400;
    font-size: clamp(0.9rem, 1.2vw, 1.1rem);
    background: transparent;
    color: #000000;
    border: 1px solid #000000;
    padding: clamp(15px, 1.5vw, 20px) clamp(25px, 3vw, 30px);
    border-radius: 100px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.why-button:hover {
    background: #000000;
    color: #F7F3ED;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.why-right {
    flex: 0 0 clamp(55%, 60vw, 65%);
    display: flex;
    flex-direction: column;
    gap: clamp(2rem, 5vh, 3.5rem);
}

.feature-item {
    display: flex;
    flex-direction: column;
    gap: clamp(1rem, 2.5vh, 1.5rem);
    
    border-radius: clamp(8px, 1.5vw, 16px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: transparent;
}






.feature-title,
.feature-description {
    position: relative;
    z-index: 1;
}

.feature-title {
    font-family: 'Nyght Serif', serif;
    font-weight: 300;
    font-size: clamp(2rem, 2.2vw, 3rem);
    line-height: clamp(1.15, 1.2, 1.25);
    color: #000000;
    margin: 0;
    font-style: italic;
}

.feature-description {
    font-family: 'Arial';
    font-weight: 400;
    font-size: clamp(1rem, 1.2vw, 1.1rem);
    line-height: clamp(1.5, 1.6, 1.7);
    color: #000000;
    margin: 0;
    max-width: clamp(400px, 80%, 600px);
}

.feature-divider {
    width: 100%;
    height: 1px;
    background-color: #000000;
    margin-top: clamp(0.5rem, 1vh, 1rem);
}

/* Mobile layout adjustments */
@media (max-width: 768px) {
    .why-content {
        flex-direction: column;
        text-align: left;
        gap: clamp(3rem, 8vh, 5rem);
    }

    .why-left {
        flex: none;
        align-items: left;
        width: 100%;
    }

    .why-right {
        flex: none;
        align-items: left;
        width: 100%;
    }

    .why-title {
        text-align: left;
        font-size: 53px;
    }

    .feature-item {
        text-align: left;
        align-items: left;
    }

    .feature-description {
        max-width: 100%;
    }
    .why-lawvriksh-section{
        margin-bottom:40px;
    }
}

/* 
Performance optimizations */
.why-lawvriksh-section * {
    will-change: transform, opacity;
}

.why-title {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.feature-item {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.feature-divider {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}