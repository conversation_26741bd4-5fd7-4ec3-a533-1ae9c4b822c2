@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap');

@font-face {
    font-family: 'Nyght Serif';
    src: url('/fonts/nyght/NyghtSerif-LightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

.cta-section {
    width: 100vw;
    height: clamp(280px, 60vh + 1rem, 587px);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 clamp(1rem, 2vw + 1rem, 84px);
    box-sizing: border-box;
    position: relative;
    gap: clamp(2rem, 10vw, 10rem);
    
}

.cta-left {
    flex: 0 0 clamp(20%, 25vw, 30%);
    max-width: clamp(250px, 30vw + 1rem, 350px);
    display: flex;
    flex-direction: column;
    gap:100px;
    justify-content: space-evenly;
    height: 100%;
    min-height: clamp(150px, 20vh, 250px);
}

.cta-right {
    flex: 0 0 clamp(60%, 65vw, 75%);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding-left: 0;
}

.cta-intro {
    font-family: 'Arial';
    font-weight: 400;
    font-size: clamp(0.9rem, 1.5vw, 1rem);
    line-height: 1.5;
    color: #575757;
    margin: 0;
}

.cta-divider {
    width: 1px;
    height: clamp(180px, 40vh + 1rem, 421px);
    background-color: #000000;
    flex-shrink: 0;
    margin-left: clamp(-5rem, -4vw + 1rem, -5.5rem);
    align-self: center;
}

.cta-link {
    font-family: 'Arial';
    font-weight: 400;
    font-size: clamp(0.9rem, 1.5vw, 1.4rem);
    color: #000000;
    text-decoration: underline;
    text-decoration-thickness: 1px;
    text-underline-offset: clamp(2px, 0.5vw, 6px);
    transition: all 0.3s ease;
    margin: 0;
}

.cta-link:hover {
    color: #6B5635;
    text-decoration-thickness: 2px;
    transform: translateY(-1px);
}

.cta-quote-container {
    position: relative;
    min-height: clamp(120px, 15vh, 200px);
    display: flex;
    align-items: center;
    width: 100%;
}

.cta-main-text {
    font-family: "Nyght Serif", serif;
    font-style: italic;
    font-size: clamp(1.8rem, 2.5vw, 3rem);
    line-height: clamp(1.2, 1.3, 1.4);
    color: #000000;
    max-width: clamp(300px, 45vw, 1000px);
    margin: 0;
    font-weight:lighter;
    text-align: left;
}

/* Mobile layout adjustments */
@media (max-width: 768px) {
    .cta-section {
        display: grid;
        grid-template-columns: auto 1fr;
        grid-template-rows: auto auto;
        
        max-height: 667px;
        justify-content: center;
        align-items: center;
        padding: clamp(1.5rem, 4vw, 2rem) clamp(1rem, 3vw, 1.5rem);
        gap: clamp(2rem, 5vw, 2.5rem) clamp(0.75rem, 2vw, 1rem);
    }

    .cta-right {
        grid-row: 1;
        grid-column: 1 / -1;
        max-width: 100%;
        width: 100%;
        justify-content: flex-start;
        padding-left: 0;
    }

    .cta-quote-container {
        min-height: clamp(100px, 12vh, 150px);
        width: 100%;
    }

    .cta-main-text {
        text-align: left;
        font-size: clamp(2.2rem, 7vw, 3rem);
        line-height: 1.25;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }

    .cta-divider {
        grid-row: 2;
        grid-column: 1;
        width: 1px;
        height: clamp(80px, 15vh, 120px);
        flex-shrink: 0;
        margin: 0;
        justify-self: start;
        align-self: start;
    }

    .cta-left {
        grid-row: 2;
        grid-column: 2;
        flex: none;
        max-width: none;
        align-items: flex-start;
        text-align: left;
        max-height:200px;
        gap: clamp(1rem, 3vw, 1.25rem);
        padding-left: 0;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-self: start;
    }

    .cta-intro {
        text-align: left;
        font-size: 15px;
        line-height: 1.5;
        color: #666;
        margin: 0 0 clamp(1rem, 3vw, 1.25rem) 0;
    }

    .cta-link {
        font-size: 20px;
        text-align: left;
        align-self: flex-start;
        font-weight: normal;
        color: #4c4c4c;
        margin: 0;
    }
}

/* 
Performance optimizations */
.cta-section * {
    will-change: transform, opacity;
}

.cta-main-text {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.cta-link {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.cta-divider {
    transform: translateZ(0);
    backface-visibility: hidden;
}