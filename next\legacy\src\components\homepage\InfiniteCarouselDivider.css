@font-face {
    font-family: "Nyght Serif";
    src: url('/fonts/nyght/NyghtSerif-LightItalic.woff2') format('woff2');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

.infinite-carousel-divider {
    width: 100vw;
    height: clamp(50px, 14vh + 1rem, 200px);
    background-image: url('/ctabg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: clamp(3rem, 15vh + 1rem, 237px);
    
}

.infinite-carousel-divider::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.carousel-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    z-index: 2;
}

.carousel-track {
    display: flex;
    align-items: center;
    height: 100%;
    animation: scroll-left 60s linear infinite;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Pause animation on hover for better UX */
.infinite-carousel-divider:hover .carousel-track {
    animation-play-state: paused;
}

.carousel-text {
    font-family: "Nyght Serif", serif;
    font-weight: 300;
    font-style: italic;
    font-size: clamp(20px, 2vw, 50px);
    color: #000000;
    padding: 0 clamp(2rem, 6vw, 8rem);
    line-height: clamp(1.1, 1.2, 1.3);
    text-align: center;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: clamp(400px, 50vw, 600px);
    display: flex;
    align-items: center;
    justify-content: center;
    
}

/* Smooth infinite scroll animation */
@keyframes scroll-left {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Performance optimizations */
.carousel-track {
    transform: translateZ(0);
    backface-visibility: hidden;
}

.carousel-text {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .carousel-track {
        animation-duration: 120s;
    }
}
@media (max-width: 768px) {
    .infinite-carousel-divider {
        height: 71px;
    }
}