<svg xmlns="http://www.w3.org/2000/svg" width="800" height="600" viewBox="0 0 800 600" preserveAspectRatio="xMidYMid slice">
  <defs>
    <linearGradient id="g" x1="0" x2="1" y1="0" y2="1">
      <stop offset="0%" stop-color="#f0f0f0"/>
      <stop offset="100%" stop-color="#d9d9d9"/>
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#g)"/>
  <g fill="#9aa3af">
    <rect x="200" y="160" width="400" height="280" rx="8"/>
    <circle cx="400" cy="300" r="80" fill="#cbd5e1"/>
  </g>
  <text x="50%" y="90%" dominant-baseline="middle" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="#6b7280">Image Placeholder</text>
</svg>

