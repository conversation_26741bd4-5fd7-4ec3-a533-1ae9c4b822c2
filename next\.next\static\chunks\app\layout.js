/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f72488b10bf4\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcQkVUQVxcQkVUQS1GUk9OVEVORFxcbmV4dFxcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY3MjQ4OGIxMGJmNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _legacy_src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../legacy/src/contexts/AuthContext */ \"(app-pages-browser)/./legacy/src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Providers(param) {\n    let { children } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_legacy_src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\BETA\\\\BETA-FRONTEND\\\\next\\\\app\\\\providers.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n_c = Providers;\nvar _c;\n$RefreshReg$(_c, \"Providers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wcm92aWRlcnMudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUMwQjtBQUN3QztBQUVuRCxTQUFTRSxVQUFVLEtBQTJDO1FBQTNDLEVBQUVDLFFBQVEsRUFBaUMsR0FBM0M7SUFDaEMscUJBQU8sOERBQUNGLDBFQUFZQTtrQkFBRUU7Ozs7OztBQUN4QjtLQUZ3QkQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcQkVUQVxcQkVUQS1GUk9OVEVORFxcbmV4dFxcYXBwXFxwcm92aWRlcnMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIi4uL2xlZ2FjeS9zcmMvY29udGV4dHMvQXV0aENvbnRleHRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgcmV0dXJuIDxBdXRoUHJvdmlkZXI+e2NoaWxkcmVufTwvQXV0aFByb3ZpZGVyPjtcbn1cblxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQXV0aFByb3ZpZGVyIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/providers.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./legacy/src/contexts/AuthContext.tsx":
/*!*********************************************!*\
  !*** ./legacy/src/contexts/AuthContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/authService */ \"(app-pages-browser)/./legacy/src/services/authService.ts\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Check for existing authentication on app load\n            const initializeAuth = {\n                \"AuthProvider.useEffect.initializeAuth\": async ()=>{\n                    setLoading(true);\n                    try {\n                        if (_services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.isAuthenticated()) {\n                            const userData = await _services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.validateToken();\n                            setUser(userData);\n                        }\n                    } catch (error) {\n                        console.error('Auth initialization failed:', error);\n                        _services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const login = async (email, password)=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.login({\n                email,\n                password\n            });\n            const userData = await _services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n            setUser(userData);\n            setLoading(false);\n            return true;\n        } catch (error) {\n            console.error('Login failed:', error);\n            setLoading(false);\n            return false;\n        }\n    };\n    const logout = ()=>{\n        _services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n        setUser(null);\n    };\n    const refreshUser = async ()=>{\n        try {\n            if (_services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.isAuthenticated()) {\n                const userData = await _services_authService__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n                setUser(userData);\n            }\n        } catch (error) {\n            console.error('Failed to refresh user:', error);\n            logout();\n        }\n    };\n    const value = {\n        user,\n        isAuthenticated: !!user,\n        isAdmin: !!(user === null || user === void 0 ? void 0 : user.is_admin),\n        login,\n        logout,\n        loading,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\BETA\\\\BETA-FRONTEND\\\\next\\\\legacy\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./legacy/src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./legacy/src/services/api.ts":
/*!************************************!*\
  !*** ./legacy/src/services/api.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   MOCK_MODE: () => (/* binding */ MOCK_MODE),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   isApiError: () => (/* binding */ isApiError),\n/* harmony export */   mockDelay: () => (/* binding */ mockDelay),\n/* harmony export */   withErrorHandling: () => (/* binding */ withErrorHandling)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Base API service configuration and utilities\n// API Configuration\nconst API_CONFIG = {\n    BASE_URL: typeof process !== 'undefined' && process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',\n    TIMEOUT: 5000,\n    RETRY_ATTEMPTS: 3\n};\n// API Client class for making HTTP requests\nclass ApiClient {\n    getAuthHeaders() {\n        const token = localStorage.getItem('authToken');\n        return token ? {\n            Authorization: \"Bearer \".concat(token)\n        } : {};\n    }\n    async makeRequest(config) {\n        const { method, url, data, params, headers = {} } = config;\n        // Build full URL - ensure proper path concatenation\n        const cleanUrl = url.startsWith('/') ? url.slice(1) : url;\n        const baseUrlWithSlash = this.baseURL.endsWith('/') ? this.baseURL : this.baseURL + '/';\n        const fullUrl = new URL(cleanUrl, baseUrlWithSlash);\n        // Create cache key for GET requests to prevent duplicates\n        const cacheKey = method === 'GET' ? \"\".concat(method, \":\").concat(fullUrl.toString()) : null;\n        // Rate limiting: check if we made the same request too recently\n        if (cacheKey && this.rateLimitingEnabled) {\n            const lastTime = this.lastRequestTime.get(cacheKey);\n            const now = Date.now();\n            // Different intervals for different endpoints\n            let interval = this.minRequestInterval;\n            if (url.includes('/leaderboard/around-me')) {\n                interval = 100; // Allow around-me requests more frequently\n            }\n            if (lastTime && now - lastTime < interval) {\n                console.log(\"⏱️ Rate limited: \".concat(method, \" \").concat(fullUrl.toString()));\n                return Promise.reject(new ApiError('Rate limited: too many requests', 429));\n            }\n            this.lastRequestTime.set(cacheKey, now);\n        }\n        // Enable rate limiting after first few requests\n        if (!this.rateLimitingEnabled && this.lastRequestTime.size > 3) {\n            this.rateLimitingEnabled = true;\n            console.log('🔒 Rate limiting enabled after initial load');\n        }\n        // Check if we already have this request in progress (for GET requests only)\n        if (cacheKey && this.requestCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD04 Using cached request: \".concat(method, \" \").concat(fullUrl.toString()));\n            return this.requestCache.get(cacheKey);\n        }\n        // console.log(`🌐 API Request: ${method} ${fullUrl.toString()}`);\n        // Add query parameters\n        if (params) {\n            Object.keys(params).forEach((key)=>{\n                if (params[key] !== undefined && params[key] !== null) {\n                    fullUrl.searchParams.append(key, params[key].toString());\n                }\n            });\n        }\n        // Prepare request options\n        const requestOptions = {\n            method,\n            headers: {\n                'Content-Type': 'application/json',\n                ...this.getAuthHeaders(),\n                ...headers\n            },\n            signal: AbortSignal.timeout(this.timeout)\n        };\n        // Add body for POST/PUT requests\n        if (data && (method === 'POST' || method === 'PUT')) {\n            requestOptions.body = JSON.stringify(data);\n        }\n        // Create the actual request promise\n        const requestPromise = this.executeRequest(fullUrl.toString(), requestOptions, method);\n        // Cache GET requests to prevent duplicates\n        if (cacheKey) {\n            this.requestCache.set(cacheKey, requestPromise);\n            // Clean up cache after request completes or after 30 seconds\n            requestPromise.finally(()=>{\n                setTimeout(()=>{\n                    this.requestCache.delete(cacheKey);\n                }, 30000); // Keep cache for 30 seconds\n            });\n        }\n        return requestPromise;\n    }\n    async executeRequest(url, options, method) {\n        try {\n            const response = await fetch(url, options);\n            // console.log(`📡 API Response: ${response.status} ${response.statusText}`);\n            // Handle different response types\n            let responseData;\n            const contentType = response.headers.get('content-type');\n            if (contentType === null || contentType === void 0 ? void 0 : contentType.includes('application/json')) {\n                responseData = await response.json();\n            } else if (contentType === null || contentType === void 0 ? void 0 : contentType.includes('text/')) {\n                responseData = await response.text();\n            } else {\n                responseData = await response.blob();\n            }\n            if (!response.ok) {\n                console.error(\"❌ API Error: \".concat(response.status, \" \").concat(response.statusText), responseData);\n                // Extract error message from different possible response formats\n                let errorMessage = \"HTTP \".concat(response.status);\n                if (responseData) {\n                    // Handle structured error response from backend error handlers\n                    if (responseData.error && responseData.error.message) {\n                        errorMessage = responseData.error.message;\n                    } else if (responseData.detail) {\n                        errorMessage = responseData.detail;\n                    } else if (responseData.message) {\n                        errorMessage = responseData.message;\n                    } else if (typeof responseData === 'string') {\n                        errorMessage = responseData;\n                    }\n                }\n                throw new Error(errorMessage);\n            }\n            // console.log(`✅ API Success: ${method} ${url}`, responseData);\n            return {\n                data: responseData,\n                status: response.status\n            };\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 API Request Failed: \".concat(method, \" \").concat(url), error);\n            if (error instanceof ApiError) {\n                throw error;\n            }\n            // Handle specific network errors\n            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {\n                console.warn(\"\\uD83C\\uDF10 Network error for \".concat(url, \", this might be a CORS or connectivity issue\"));\n                throw new ApiError('Network connection failed. Please check your internet connection.', 0, error);\n            }\n            // Handle ERR_INSUFFICIENT_RESOURCES\n            if (error instanceof Error && error.message.includes('ERR_INSUFFICIENT_RESOURCES')) {\n                console.warn(\"⚠️ Too many requests for \".concat(url, \", implementing backoff\"));\n                throw new ApiError('Too many requests. Please wait a moment and try again.', 429, error);\n            }\n            // Handle timeout errors\n            if (error instanceof DOMException && error.name === 'TimeoutError') {\n                console.warn(\"⏰ Request timeout for \".concat(url));\n                throw new ApiError('Request timed out. Please try again.', 408, error);\n            }\n            // Handle other network errors\n            throw new ApiError(error instanceof Error ? error.message : 'Network error occurred', 0, error);\n        }\n    }\n    async get(url, params, headers) {\n        return this.makeRequest({\n            method: 'GET',\n            url,\n            params,\n            headers\n        });\n    }\n    async post(url, data, headers) {\n        return this.makeRequest({\n            method: 'POST',\n            url,\n            data,\n            headers\n        });\n    }\n    async put(url, data, headers) {\n        return this.makeRequest({\n            method: 'PUT',\n            url,\n            data,\n            headers\n        });\n    }\n    async delete(url, headers) {\n        return this.makeRequest({\n            method: 'DELETE',\n            url,\n            headers\n        });\n    }\n    constructor(baseURL = API_CONFIG.BASE_URL, timeout = API_CONFIG.TIMEOUT){\n        this.requestCache = new Map();\n        this.lastRequestTime = new Map();\n        this.minRequestInterval = 200; // Minimum 200ms between same requests\n        this.rateLimitingEnabled = false; // Disable initially to allow first load\n        this.baseURL = baseURL;\n        this.timeout = timeout;\n    }\n}\n// Custom API Error class\nclass ApiError extends Error {\n    constructor(message, status, details){\n        super(message);\n        this.name = 'ApiError';\n        this.status = status;\n        this.details = details;\n    }\n}\n// Create singleton API client instance\nconst apiClient = new ApiClient();\n// Utility functions for API responses\nconst handleApiError = (error)=>{\n    if (error instanceof ApiError) {\n        return error;\n    }\n    if (error instanceof Error) {\n        return new ApiError(error.message, 0);\n    }\n    return new ApiError('Unknown error occurred', 0);\n};\nconst isApiError = (error)=>{\n    return error instanceof ApiError;\n};\n// Response wrapper for consistent error handling\nconst withErrorHandling = async (apiCall)=>{\n    try {\n        const response = await apiCall();\n        return response.data;\n    } catch (error) {\n        throw handleApiError(error);\n    }\n};\n// Mock mode flag for development\nconst MOCK_MODE = (typeof process !== 'undefined' && process.env.NEXT_PUBLIC_MOCK_MODE) === 'true';\n// Mock delay utility for simulating network latency\nconst mockDelay = function() {\n    let ms = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 500;\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n};\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./legacy/src/services/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./legacy/src/services/authService.ts":
/*!********************************************!*\
  !*** ./legacy/src/services/authService.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./legacy/src/services/api.ts\");\n// Authentication service for handling login, signup, and user management\n\nclass AuthService {\n    /**\r\n   * User signup/registration\r\n   */ async signup(userData) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)();\n            // Mock validation\n            if (userData.email === this.MOCK_ADMIN.email) {\n                throw new Error('Email already registered');\n            }\n            // Return mock user response\n            return {\n                user_id: Math.floor(Math.random() * 1000) + 2,\n                name: userData.name,\n                email: userData.email,\n                created_at: new Date().toISOString(),\n                total_points: 0,\n                shares_count: 0,\n                current_rank: undefined,\n                is_admin: false\n            };\n        }\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/signup', userData));\n        // Store user data on successful signup (but note: no auth token yet)\n        if (response.user_id) {\n            localStorage.setItem('userData', JSON.stringify(response));\n            console.log('✅ User data stored after signup:', response);\n        }\n        return response;\n    }\n    /**\r\n   * User login\r\n   */ async login(credentials) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)();\n            // Mock authentication\n            if (credentials.email === this.MOCK_ADMIN.email && credentials.password === this.MOCK_ADMIN.password) {\n                const mockToken = 'mock-jwt-token-' + Date.now();\n                // Store token and user data\n                localStorage.setItem('authToken', mockToken);\n                localStorage.setItem('userData', JSON.stringify(this.MOCK_ADMIN.user));\n                return {\n                    access_token: mockToken,\n                    token_type: 'bearer',\n                    expires_in: 3600\n                };\n            }\n            throw new Error('Invalid email or password');\n        }\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/login', credentials));\n        // Store token on successful login\n        if (response.access_token) {\n            localStorage.setItem('authToken', response.access_token);\n            // Get user data after successful login\n            try {\n                const userData = await this.getCurrentUser();\n                localStorage.setItem('userData', JSON.stringify(userData));\n            } catch (error) {\n                console.warn('Failed to fetch user data after login:', error);\n            }\n        }\n        return response;\n    }\n    /**\r\n   * Get current user information\r\n   */ async getCurrentUser() {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)(200);\n            const userData = localStorage.getItem('userData');\n            if (userData) {\n                return JSON.parse(userData);\n            }\n            throw new Error('User not authenticated');\n        }\n        return (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.get('/auth/me'));\n    }\n    /**\r\n   * Update user profile\r\n   */ async updateProfile(profileData) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)();\n            const userData = localStorage.getItem('userData');\n            if (!userData) {\n                throw new Error('User not authenticated');\n            }\n            const currentUser = JSON.parse(userData);\n            const updatedUser = {\n                ...currentUser,\n                ...profileData\n            };\n            localStorage.setItem('userData', JSON.stringify(updatedUser));\n            return updatedUser;\n        }\n        return (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.put('/users/profile', profileData));\n    }\n    /**\r\n   * Logout user\r\n   */ logout() {\n        localStorage.removeItem('authToken');\n        localStorage.removeItem('userData');\n    }\n    /**\r\n   * Check if user is authenticated\r\n   */ isAuthenticated() {\n        const token = localStorage.getItem('authToken');\n        const userData = localStorage.getItem('userData');\n        return !!(token && userData);\n    }\n    /**\r\n   * Check if current user is admin\r\n   */ isAdmin() {\n        const userData = localStorage.getItem('userData');\n        if (!userData) return false;\n        try {\n            const user = JSON.parse(userData);\n            return user.is_admin === true;\n        } catch (e) {\n            return false;\n        }\n    }\n    /**\r\n   * Get stored user data\r\n   */ getStoredUser() {\n        const userData = localStorage.getItem('userData');\n        if (!userData) return null;\n        try {\n            return JSON.parse(userData);\n        } catch (e) {\n            return null;\n        }\n    }\n    /**\r\n   * Refresh authentication token\r\n   */ async refreshToken() {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)();\n            if (!this.isAuthenticated()) {\n                throw new Error('User not authenticated');\n            }\n            const newToken = 'mock-jwt-token-' + Date.now();\n            localStorage.setItem('authToken', newToken);\n            return {\n                access_token: newToken,\n                token_type: 'bearer',\n                expires_in: 3600\n            };\n        }\n        return (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/refresh'));\n    }\n    /**\r\n   * Validate token and get user info\r\n   */ async validateToken() {\n        if (!this.isAuthenticated()) {\n            return null;\n        }\n        try {\n            return await this.getCurrentUser();\n        } catch (error) {\n            // Token is invalid, clear stored data\n            this.logout();\n            return null;\n        }\n    }\n    /**\r\n   * Request password reset\r\n   */ async forgotPassword(email) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)(1000);\n            // Mock validation\n            if (!email || !email.includes('@')) {\n                throw new Error('Please enter a valid email address');\n            }\n            return {\n                message: 'Password reset instructions have been sent to your email address.',\n                success: true\n            };\n        }\n        const request = {\n            email\n        };\n        return (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/forgot-password', request));\n    }\n    /**\r\n   * Reset password with token\r\n   */ async resetPassword(token, newPassword) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)(1000);\n            // Mock validation\n            if (!token) {\n                throw new Error('Invalid or expired reset token');\n            }\n            if (!newPassword || newPassword.length < 6) {\n                throw new Error('Password must be at least 6 characters long');\n            }\n            return {\n                message: 'Your password has been successfully reset. You can now log in with your new password.',\n                success: true\n            };\n        }\n        const request = {\n            token,\n            new_password: newPassword\n        };\n        return (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/auth/reset-password', request));\n    }\n    /**\r\n   * Change password for authenticated user\r\n   */ async changePassword(currentPassword, newPassword) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)(800);\n            // Mock validation\n            if (!this.isAuthenticated()) {\n                throw new Error('User not authenticated');\n            }\n            if (!currentPassword) {\n                throw new Error('Current password is required');\n            }\n            if (!newPassword || newPassword.length < 6) {\n                throw new Error('New password must be at least 6 characters long');\n            }\n            return {\n                message: 'Password changed successfully',\n                success: true\n            };\n        }\n        return (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post('/users/change-password', {\n                current_password: currentPassword,\n                new_password: newPassword\n            }));\n    }\n    /**\r\n   * Delete user account\r\n   */ async deleteAccount(password) {\n        if (_api__WEBPACK_IMPORTED_MODULE_0__.MOCK_MODE) {\n            await (0,_api__WEBPACK_IMPORTED_MODULE_0__.mockDelay)(1000);\n            if (!this.isAuthenticated()) {\n                throw new Error('User not authenticated');\n            }\n            if (!password) {\n                throw new Error('Password confirmation is required');\n            }\n            // Clear stored data\n            this.logout();\n            return {\n                message: 'Account deleted successfully'\n            };\n        }\n        const response = await (0,_api__WEBPACK_IMPORTED_MODULE_0__.withErrorHandling)(()=>_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete('/users/delete-account', {\n                password\n            }));\n        // Clear stored data after successful deletion\n        this.logout();\n        return response;\n    }\n    /**\r\n   * Check password strength\r\n   */ checkPasswordStrength(password) {\n        const feedback = [];\n        let score = 0;\n        if (password.length < 6) {\n            feedback.push('Password must be at least 6 characters long');\n        } else {\n            score += 1;\n        }\n        if (password.length >= 8) {\n            score += 1;\n        }\n        if (/[a-z]/.test(password) && /[A-Z]/.test(password)) {\n            score += 1;\n        } else {\n            feedback.push('Include both uppercase and lowercase letters');\n        }\n        if (/\\d/.test(password)) {\n            score += 1;\n        } else {\n            feedback.push('Include at least one number');\n        }\n        if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n            score += 1;\n        } else {\n            feedback.push('Include at least one special character');\n        }\n        const strengthLabels = [\n            'Very Weak',\n            'Weak',\n            'Fair',\n            'Good',\n            'Strong'\n        ];\n        const strengthLabel = strengthLabels[Math.min(score, 4)];\n        if (score >= 3) {\n            feedback.unshift(\"Password strength: \".concat(strengthLabel));\n        }\n        return {\n            score,\n            feedback,\n            isValid: score >= 2 && password.length >= 6\n        };\n    }\n    /**\r\n   * Auto-refresh token before expiration\r\n   */ startTokenRefresh() {\n        // Refresh token every 50 minutes (assuming 1-hour expiration)\n        const refreshInterval = 50 * 60 * 1000;\n        setInterval(async ()=>{\n            if (this.isAuthenticated()) {\n                try {\n                    await this.refreshToken();\n                } catch (error) {\n                    console.warn('Token refresh failed:', error);\n                // Don't logout automatically, let the user continue until next API call fails\n                }\n            }\n        }, refreshInterval);\n    }\n    constructor(){\n        // Mock admin credentials for development\n        this.MOCK_ADMIN = {\n            email: '<EMAIL>',\n            password: 'admin123',\n            user: {\n                user_id: 1,\n                name: 'Admin User',\n                email: '<EMAIL>',\n                created_at: new Date().toISOString(),\n                total_points: 0,\n                shares_count: 0,\n                current_rank: null,\n                is_admin: true\n            }\n        };\n    }\n}\n// Export singleton instance\nconst authService = new AuthService();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (authService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./legacy/src/services/authService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js":
/*!************************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = ThirdPartyScriptEmbed;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst react_1 = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nfunction ThirdPartyScriptEmbed(param) {\n    let { html, height = null, width = null, children, dataNtpc = '' } = param;\n    (0, react_1.useEffect)(()=>{\n        if (dataNtpc) {\n            // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n            // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n            // existing API.\n            performance.mark('mark_feature_usage', {\n                detail: {\n                    feature: \"next-third-parties-\".concat(dataNtpc)\n                }\n            });\n        }\n    }, [\n        dataNtpc\n    ]);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            children,\n            html ? (0, jsx_runtime_1.jsx)(\"div\", {\n                style: {\n                    height: height != null ? \"\".concat(height, \"px\") : 'auto',\n                    width: width != null ? \"\".concat(width, \"px\") : 'auto'\n                },\n                \"data-ntpc\": dataNtpc,\n                dangerouslySetInnerHTML: {\n                    __html: html\n                }\n            }) : null\n        ]\n    });\n}\n_c = ThirdPartyScriptEmbed;\nvar _c;\n$RefreshReg$(_c, \"ThirdPartyScriptEmbed\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbmV4dC90aGlyZC1wYXJ0aWVzL2Rpc3QvVGhpcmRQYXJ0eVNjcmlwdEVtYmVkLmpzIiwibWFwcGluZ3MiOiJxREFBYTtBQUViQSw4Q0FBNkM7SUFBRUcsT0FBTztBQUFLLENBQUMsRUFBQztBQUM3REQsa0JBQWUsR0FBR0c7QUFDbEIsTUFBTUMsZ0JBQWdCQyxtQkFBT0EsQ0FBQyxxR0FBbUI7QUFDakQsTUFBTUMsVUFBVUQsbUJBQU9BLENBQUMsbUZBQU87QUFDL0IsU0FBU0Ysc0JBQXNCLEtBQStEO1FBQS9ELEVBQUVJLElBQUksRUFBRUMsU0FBUyxJQUFJLEVBQUVDLFFBQVEsSUFBSSxFQUFFQyxRQUFRLEVBQUVDLFdBQVcsRUFBRSxFQUFHLEdBQS9EO0lBQzFCLElBQUdMLFFBQVFNLFNBQVMsRUFBRTtRQUNuQixJQUFJRCxVQUFVO1lBQ1YseUdBQXlHO1lBQ3pHLDZHQUE2RztZQUM3RyxnQkFBZ0I7WUFDaEJFLFlBQVlDLElBQUksQ0FBQyxzQkFBc0I7Z0JBQ25DQyxRQUFRO29CQUNKQyxTQUFTLHNCQUErQixPQUFUTDtnQkFDbkM7WUFDSjtRQUNKO0lBQ0osR0FBRztRQUFDQTtLQUFTO0lBQ2IsT0FBUSxDQUFDLEdBQUdQLGNBQWNhLElBQUksRUFBRWIsY0FBY2MsUUFBUSxFQUFFO1FBQUVSLFVBQVU7WUFBQ0E7WUFBVUgsT0FBUSxDQUFDLEdBQUdILGNBQWNlLEdBQUcsRUFBRSxPQUFPO2dCQUFFQyxPQUFPO29CQUM5R1osUUFBUUEsVUFBVSxPQUFPLEdBQVUsT0FBUEEsUUFBTyxRQUFNO29CQUN6Q0MsT0FBT0EsU0FBUyxPQUFPLEdBQVMsT0FBTkEsT0FBTSxRQUFNO2dCQUMxQztnQkFBRyxhQUFhRTtnQkFBVVUseUJBQXlCO29CQUFFQyxRQUFRZjtnQkFBSztZQUFFLEtBQU07U0FBSztJQUFDO0FBQ2hHO0tBakJTSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxCRVRBXFxCRVRBLUZST05URU5EXFxuZXh0XFxub2RlX21vZHVsZXNcXEBuZXh0XFx0aGlyZC1wYXJ0aWVzXFxkaXN0XFxUaGlyZFBhcnR5U2NyaXB0RW1iZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4ndXNlIGNsaWVudCc7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRlZmF1bHQgPSBUaGlyZFBhcnR5U2NyaXB0RW1iZWQ7XG5jb25zdCBqc3hfcnVudGltZV8xID0gcmVxdWlyZShcInJlYWN0L2pzeC1ydW50aW1lXCIpO1xuY29uc3QgcmVhY3RfMSA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcbmZ1bmN0aW9uIFRoaXJkUGFydHlTY3JpcHRFbWJlZCh7IGh0bWwsIGhlaWdodCA9IG51bGwsIHdpZHRoID0gbnVsbCwgY2hpbGRyZW4sIGRhdGFOdHBjID0gJycsIH0pIHtcbiAgICAoMCwgcmVhY3RfMS51c2VFZmZlY3QpKCgpID0+IHtcbiAgICAgICAgaWYgKGRhdGFOdHBjKSB7XG4gICAgICAgICAgICAvLyBwZXJmb3JtYW5jZS5tYXJrIGlzIGJlaW5nIHVzZWQgYXMgYSBmZWF0dXJlIHVzZSBzaWduYWwuIFdoaWxlIGl0IGlzIHRyYWRpdGlvbmFsbHkgdXNlZCBmb3IgcGVyZm9ybWFuY2VcbiAgICAgICAgICAgIC8vIGJlbmNobWFya2luZyBpdCBpcyBsb3cgb3ZlcmhlYWQgYW5kIHRodXMgY29uc2lkZXJlZCBzYWZlIHRvIHVzZSBpbiBwcm9kdWN0aW9uIGFuZCBpdCBpcyBhIHdpZGVseSBhdmFpbGFibGVcbiAgICAgICAgICAgIC8vIGV4aXN0aW5nIEFQSS5cbiAgICAgICAgICAgIHBlcmZvcm1hbmNlLm1hcmsoJ21hcmtfZmVhdHVyZV91c2FnZScsIHtcbiAgICAgICAgICAgICAgICBkZXRhaWw6IHtcbiAgICAgICAgICAgICAgICAgICAgZmVhdHVyZTogYG5leHQtdGhpcmQtcGFydGllcy0ke2RhdGFOdHBjfWAsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfSwgW2RhdGFOdHBjXSk7XG4gICAgcmV0dXJuICgoMCwganN4X3J1bnRpbWVfMS5qc3hzKShqc3hfcnVudGltZV8xLkZyYWdtZW50LCB7IGNoaWxkcmVuOiBbY2hpbGRyZW4sIGh0bWwgPyAoKDAsIGpzeF9ydW50aW1lXzEuanN4KShcImRpdlwiLCB7IHN0eWxlOiB7XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodDogaGVpZ2h0ICE9IG51bGwgPyBgJHtoZWlnaHR9cHhgIDogJ2F1dG8nLFxuICAgICAgICAgICAgICAgICAgICB3aWR0aDogd2lkdGggIT0gbnVsbCA/IGAke3dpZHRofXB4YCA6ICdhdXRvJyxcbiAgICAgICAgICAgICAgICB9LCBcImRhdGEtbnRwY1wiOiBkYXRhTnRwYywgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHsgX19odG1sOiBodG1sIH0gfSkpIDogbnVsbF0gfSkpO1xufVxuIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiZGVmYXVsdCIsIlRoaXJkUGFydHlTY3JpcHRFbWJlZCIsImpzeF9ydW50aW1lXzEiLCJyZXF1aXJlIiwicmVhY3RfMSIsImh0bWwiLCJoZWlnaHQiLCJ3aWR0aCIsImNoaWxkcmVuIiwiZGF0YU50cGMiLCJ1c2VFZmZlY3QiLCJwZXJmb3JtYW5jZSIsIm1hcmsiLCJkZXRhaWwiLCJmZWF0dXJlIiwianN4cyIsIkZyYWdtZW50IiwianN4Iiwic3R5bGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js":
/*!************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/ga.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.GoogleAnalytics = GoogleAnalytics;\nexports.sendGAEvent = sendGAEvent;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// TODO: Evaluate import 'client only'\nconst react_1 = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(app-pages-browser)/./node_modules/next/dist/api/script.js\"));\nlet currDataLayerName = undefined;\nfunction GoogleAnalytics(props) {\n    const { gaId, debugMode, dataLayerName = 'dataLayer', nonce } = props;\n    if (currDataLayerName === undefined) {\n        currDataLayerName = dataLayerName;\n    }\n    (0, react_1.useEffect)(()=>{\n        // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n        // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n        // existing API.\n        // The performance measurement will be handled by Chrome Aurora\n        performance.mark('mark_feature_usage', {\n            detail: {\n                feature: 'next-third-parties-ga'\n            }\n        });\n    }, []);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-ga-init\",\n                dangerouslySetInnerHTML: {\n                    __html: \"\\n          window['\".concat(dataLayerName, \"'] = window['\").concat(dataLayerName, \"'] || [];\\n          function gtag(){window['\").concat(dataLayerName, \"'].push(arguments);}\\n          gtag('js', new Date());\\n\\n          gtag('config', '\").concat(gaId, \"' \").concat(debugMode ? \",{ 'debug_mode': true }\" : '', \");\")\n                },\n                nonce: nonce\n            }),\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-ga\",\n                src: \"https://www.googletagmanager.com/gtag/js?id=\".concat(gaId),\n                nonce: nonce\n            })\n        ]\n    });\n}\n_c = GoogleAnalytics;\nfunction sendGAEvent() {\n    for(var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++){\n        _args[_key] = arguments[_key];\n    }\n    if (currDataLayerName === undefined) {\n        console.warn(\"@next/third-parties: GA has not been initialized\");\n        return;\n    }\n    if (window[currDataLayerName]) {\n        window[currDataLayerName].push(arguments);\n    } else {\n        console.warn(\"@next/third-parties: GA dataLayer \".concat(currDataLayerName, \" does not exist\"));\n    }\n}\nvar _c;\n$RefreshReg$(_c, \"GoogleAnalytics\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js":
/*!*************************************************************!*\
  !*** ./node_modules/@next/third-parties/dist/google/gtm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __importDefault = (void 0) && (void 0).__importDefault || function(mod) {\n    return mod && mod.__esModule ? mod : {\n        \"default\": mod\n    };\n};\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.sendGTMEvent = void 0;\nexports.GoogleTagManager = GoogleTagManager;\nconst jsx_runtime_1 = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// TODO: Evaluate import 'client only'\nconst react_1 = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst script_1 = __importDefault(__webpack_require__(/*! next/script */ \"(app-pages-browser)/./node_modules/next/dist/api/script.js\"));\nlet currDataLayerName = 'dataLayer';\nfunction GoogleTagManager(props) {\n    const { gtmId, gtmScriptUrl = 'https://www.googletagmanager.com/gtm.js', dataLayerName = 'dataLayer', auth, preview, dataLayer, nonce } = props;\n    currDataLayerName = dataLayerName;\n    const gtmLayer = dataLayerName !== 'dataLayer' ? \"&l=\".concat(dataLayerName) : '';\n    const gtmAuth = auth ? \"&gtm_auth=\".concat(auth) : '';\n    const gtmPreview = preview ? \"&gtm_preview=\".concat(preview, \"&gtm_cookies_win=x\") : '';\n    (0, react_1.useEffect)(()=>{\n        // performance.mark is being used as a feature use signal. While it is traditionally used for performance\n        // benchmarking it is low overhead and thus considered safe to use in production and it is a widely available\n        // existing API.\n        // The performance measurement will be handled by Chrome Aurora\n        performance.mark('mark_feature_usage', {\n            detail: {\n                feature: 'next-third-parties-gtm'\n            }\n        });\n    }, []);\n    return (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {\n        children: [\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-gtm-init\",\n                dangerouslySetInnerHTML: {\n                    __html: \"\\n      (function(w,l){\\n        w[l]=w[l]||[];\\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\\n        \".concat(dataLayer ? \"w[l].push(\".concat(JSON.stringify(dataLayer), \")\") : '', \"\\n      })(window,'\").concat(dataLayerName, \"');\")\n                },\n                nonce: nonce\n            }),\n            (0, jsx_runtime_1.jsx)(script_1.default, {\n                id: \"_next-gtm\",\n                \"data-ntpc\": \"GTM\",\n                src: \"\".concat(gtmScriptUrl, \"?id=\").concat(gtmId).concat(gtmLayer).concat(gtmAuth).concat(gtmPreview),\n                nonce: nonce\n            })\n        ]\n    });\n}\n_c = GoogleTagManager;\nconst sendGTMEvent = (data, dataLayerName)=>{\n    // special case if we are sending events before GTM init and we have custom dataLayerName\n    const dataLayer = dataLayerName || currDataLayerName;\n    // define dataLayer so we can still queue up events before GTM init\n    window[dataLayer] = window[dataLayer] || [];\n    window[dataLayer].push(data);\n};\nexports.sendGTMEvent = sendGTMEvent;\nvar _c;\n$RefreshReg$(_c, \"GoogleTagManager\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbmV4dC90aGlyZC1wYXJ0aWVzL2Rpc3QvZ29vZ2xlL2d0bS5qcyIsIm1hcHBpbmdzIjoicURBQWE7QUFFYixJQUFJQSxrQkFBa0IsQ0FBQyxNQUFHLEtBQUssT0FBRyxFQUFFQSxlQUFlLElBQUssU0FBVUMsR0FBRztJQUNqRSxPQUFPLE9BQVFBLElBQUlDLFVBQVUsR0FBSUQsTUFBTTtRQUFFLFdBQVdBO0lBQUk7QUFDNUQ7QUFDQUUsOENBQTZDO0lBQUVHLE9BQU87QUFBSyxDQUFDLEVBQUM7QUFDN0RELG9CQUFvQixHQUFHLEtBQUs7QUFDNUJBLHdCQUF3QixHQUFHRztBQUMzQixNQUFNQyxnQkFBZ0JDLG1CQUFPQSxDQUFDLHFHQUFtQjtBQUNqRCxzQ0FBc0M7QUFDdEMsTUFBTUMsVUFBVUQsbUJBQU9BLENBQUMsbUZBQU87QUFDL0IsTUFBTUUsV0FBV1osZ0JBQWdCVSxtQkFBT0EsQ0FBQywrRUFBYTtBQUN0RCxJQUFJRyxvQkFBb0I7QUFDeEIsU0FBU0wsaUJBQWlCTSxLQUFLO0lBQzNCLE1BQU0sRUFBRUMsS0FBSyxFQUFFQyxlQUFlLHlDQUF5QyxFQUFFQyxnQkFBZ0IsV0FBVyxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRUMsU0FBUyxFQUFFQyxLQUFLLEVBQUcsR0FBR1A7SUFDM0lELG9CQUFvQkk7SUFDcEIsTUFBTUssV0FBV0wsa0JBQWtCLGNBQWMsTUFBb0IsT0FBZEEsaUJBQWtCO0lBQ3pFLE1BQU1NLFVBQVVMLE9BQU8sYUFBa0IsT0FBTEEsUUFBUztJQUM3QyxNQUFNTSxhQUFhTCxVQUFVLGdCQUF3QixPQUFSQSxTQUFRLHdCQUFzQjtJQUMxRSxJQUFHUixRQUFRYyxTQUFTLEVBQUU7UUFDbkIseUdBQXlHO1FBQ3pHLDZHQUE2RztRQUM3RyxnQkFBZ0I7UUFDaEIsK0RBQStEO1FBQy9EQyxZQUFZQyxJQUFJLENBQUMsc0JBQXNCO1lBQ25DQyxRQUFRO2dCQUNKQyxTQUFTO1lBQ2I7UUFDSjtJQUNKLEdBQUcsRUFBRTtJQUNMLE9BQVEsQ0FBQyxHQUFHcEIsY0FBY3FCLElBQUksRUFBRXJCLGNBQWNzQixRQUFRLEVBQUU7UUFBRUMsVUFBVTtZQUFFLElBQUd2QixjQUFjd0IsR0FBRyxFQUFFckIsU0FBU3NCLE9BQU8sRUFBRTtnQkFBRUMsSUFBSTtnQkFBa0JDLHlCQUF5QjtvQkFDL0lDLFFBQVEsb0lBS1RwQixPQURURyxZQUFZLGFBQXVDLE9BQTFCa0IsS0FBS0MsU0FBUyxDQUFDbkIsWUFBVyxPQUFLLElBQUcsdUJBQ3BDLE9BQWRILGVBQWM7Z0JBQ2pCO2dCQUFHSSxPQUFPQTtZQUFNO1lBQUssSUFBR1osY0FBY3dCLEdBQUcsRUFBRXJCLFNBQVNzQixPQUFPLEVBQUU7Z0JBQUVDLElBQUk7Z0JBQWEsYUFBYTtnQkFBT0ssS0FBSyxHQUFzQnpCLE9BQW5CQyxjQUFhLFFBQWNNLE9BQVJQLE9BQW1CUSxPQUFYRCxVQUFxQkUsT0FBVkQsU0FBcUIsT0FBWEM7Z0JBQWNILE9BQU9BO1lBQU07U0FBRztJQUFDO0FBQzNNO0tBekJTYjtBQTBCVCxNQUFNRCxlQUFlLENBQUNrQyxNQUFNeEI7SUFDeEIseUZBQXlGO0lBQ3pGLE1BQU1HLFlBQVlILGlCQUFpQko7SUFDbkMsbUVBQW1FO0lBQ25FNkIsTUFBTSxDQUFDdEIsVUFBVSxHQUFHc0IsTUFBTSxDQUFDdEIsVUFBVSxJQUFJLEVBQUU7SUFDM0NzQixNQUFNLENBQUN0QixVQUFVLENBQUN1QixJQUFJLENBQUNGO0FBQzNCO0FBQ0FwQyxvQkFBb0IsR0FBR0UiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcQkVUQVxcQkVUQS1GUk9OVEVORFxcbmV4dFxcbm9kZV9tb2R1bGVzXFxAbmV4dFxcdGhpcmQtcGFydGllc1xcZGlzdFxcZ29vZ2xlXFxndG0uanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4ndXNlIGNsaWVudCc7XG52YXIgX19pbXBvcnREZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydERlZmF1bHQpIHx8IGZ1bmN0aW9uIChtb2QpIHtcbiAgICByZXR1cm4gKG1vZCAmJiBtb2QuX19lc01vZHVsZSkgPyBtb2QgOiB7IFwiZGVmYXVsdFwiOiBtb2QgfTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnNlbmRHVE1FdmVudCA9IHZvaWQgMDtcbmV4cG9ydHMuR29vZ2xlVGFnTWFuYWdlciA9IEdvb2dsZVRhZ01hbmFnZXI7XG5jb25zdCBqc3hfcnVudGltZV8xID0gcmVxdWlyZShcInJlYWN0L2pzeC1ydW50aW1lXCIpO1xuLy8gVE9ETzogRXZhbHVhdGUgaW1wb3J0ICdjbGllbnQgb25seSdcbmNvbnN0IHJlYWN0XzEgPSByZXF1aXJlKFwicmVhY3RcIik7XG5jb25zdCBzY3JpcHRfMSA9IF9faW1wb3J0RGVmYXVsdChyZXF1aXJlKFwibmV4dC9zY3JpcHRcIikpO1xubGV0IGN1cnJEYXRhTGF5ZXJOYW1lID0gJ2RhdGFMYXllcic7XG5mdW5jdGlvbiBHb29nbGVUYWdNYW5hZ2VyKHByb3BzKSB7XG4gICAgY29uc3QgeyBndG1JZCwgZ3RtU2NyaXB0VXJsID0gJ2h0dHBzOi8vd3d3Lmdvb2dsZXRhZ21hbmFnZXIuY29tL2d0bS5qcycsIGRhdGFMYXllck5hbWUgPSAnZGF0YUxheWVyJywgYXV0aCwgcHJldmlldywgZGF0YUxheWVyLCBub25jZSwgfSA9IHByb3BzO1xuICAgIGN1cnJEYXRhTGF5ZXJOYW1lID0gZGF0YUxheWVyTmFtZTtcbiAgICBjb25zdCBndG1MYXllciA9IGRhdGFMYXllck5hbWUgIT09ICdkYXRhTGF5ZXInID8gYCZsPSR7ZGF0YUxheWVyTmFtZX1gIDogJyc7XG4gICAgY29uc3QgZ3RtQXV0aCA9IGF1dGggPyBgJmd0bV9hdXRoPSR7YXV0aH1gIDogJyc7XG4gICAgY29uc3QgZ3RtUHJldmlldyA9IHByZXZpZXcgPyBgJmd0bV9wcmV2aWV3PSR7cHJldmlld30mZ3RtX2Nvb2tpZXNfd2luPXhgIDogJyc7XG4gICAgKDAsIHJlYWN0XzEudXNlRWZmZWN0KSgoKSA9PiB7XG4gICAgICAgIC8vIHBlcmZvcm1hbmNlLm1hcmsgaXMgYmVpbmcgdXNlZCBhcyBhIGZlYXR1cmUgdXNlIHNpZ25hbC4gV2hpbGUgaXQgaXMgdHJhZGl0aW9uYWxseSB1c2VkIGZvciBwZXJmb3JtYW5jZVxuICAgICAgICAvLyBiZW5jaG1hcmtpbmcgaXQgaXMgbG93IG92ZXJoZWFkIGFuZCB0aHVzIGNvbnNpZGVyZWQgc2FmZSB0byB1c2UgaW4gcHJvZHVjdGlvbiBhbmQgaXQgaXMgYSB3aWRlbHkgYXZhaWxhYmxlXG4gICAgICAgIC8vIGV4aXN0aW5nIEFQSS5cbiAgICAgICAgLy8gVGhlIHBlcmZvcm1hbmNlIG1lYXN1cmVtZW50IHdpbGwgYmUgaGFuZGxlZCBieSBDaHJvbWUgQXVyb3JhXG4gICAgICAgIHBlcmZvcm1hbmNlLm1hcmsoJ21hcmtfZmVhdHVyZV91c2FnZScsIHtcbiAgICAgICAgICAgIGRldGFpbDoge1xuICAgICAgICAgICAgICAgIGZlYXR1cmU6ICduZXh0LXRoaXJkLXBhcnRpZXMtZ3RtJyxcbiAgICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4gKCgwLCBqc3hfcnVudGltZV8xLmpzeHMpKGpzeF9ydW50aW1lXzEuRnJhZ21lbnQsIHsgY2hpbGRyZW46IFsoMCwganN4X3J1bnRpbWVfMS5qc3gpKHNjcmlwdF8xLmRlZmF1bHQsIHsgaWQ6IFwiX25leHQtZ3RtLWluaXRcIiwgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICAgICAgX19odG1sOiBgXG4gICAgICAoZnVuY3Rpb24odyxsKXtcbiAgICAgICAgd1tsXT13W2xdfHxbXTtcbiAgICAgICAgd1tsXS5wdXNoKHsnZ3RtLnN0YXJ0JzogbmV3IERhdGUoKS5nZXRUaW1lKCksZXZlbnQ6J2d0bS5qcyd9KTtcbiAgICAgICAgJHtkYXRhTGF5ZXIgPyBgd1tsXS5wdXNoKCR7SlNPTi5zdHJpbmdpZnkoZGF0YUxheWVyKX0pYCA6ICcnfVxuICAgICAgfSkod2luZG93LCcke2RhdGFMYXllck5hbWV9Jyk7YCxcbiAgICAgICAgICAgICAgICB9LCBub25jZTogbm9uY2UgfSksICgwLCBqc3hfcnVudGltZV8xLmpzeCkoc2NyaXB0XzEuZGVmYXVsdCwgeyBpZDogXCJfbmV4dC1ndG1cIiwgXCJkYXRhLW50cGNcIjogXCJHVE1cIiwgc3JjOiBgJHtndG1TY3JpcHRVcmx9P2lkPSR7Z3RtSWR9JHtndG1MYXllcn0ke2d0bUF1dGh9JHtndG1QcmV2aWV3fWAsIG5vbmNlOiBub25jZSB9KV0gfSkpO1xufVxuY29uc3Qgc2VuZEdUTUV2ZW50ID0gKGRhdGEsIGRhdGFMYXllck5hbWUpID0+IHtcbiAgICAvLyBzcGVjaWFsIGNhc2UgaWYgd2UgYXJlIHNlbmRpbmcgZXZlbnRzIGJlZm9yZSBHVE0gaW5pdCBhbmQgd2UgaGF2ZSBjdXN0b20gZGF0YUxheWVyTmFtZVxuICAgIGNvbnN0IGRhdGFMYXllciA9IGRhdGFMYXllck5hbWUgfHwgY3VyckRhdGFMYXllck5hbWU7XG4gICAgLy8gZGVmaW5lIGRhdGFMYXllciBzbyB3ZSBjYW4gc3RpbGwgcXVldWUgdXAgZXZlbnRzIGJlZm9yZSBHVE0gaW5pdFxuICAgIHdpbmRvd1tkYXRhTGF5ZXJdID0gd2luZG93W2RhdGFMYXllcl0gfHwgW107XG4gICAgd2luZG93W2RhdGFMYXllcl0ucHVzaChkYXRhKTtcbn07XG5leHBvcnRzLnNlbmRHVE1FdmVudCA9IHNlbmRHVE1FdmVudDtcbiJdLCJuYW1lcyI6WyJfX2ltcG9ydERlZmF1bHQiLCJtb2QiLCJfX2VzTW9kdWxlIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJzZW5kR1RNRXZlbnQiLCJHb29nbGVUYWdNYW5hZ2VyIiwianN4X3J1bnRpbWVfMSIsInJlcXVpcmUiLCJyZWFjdF8xIiwic2NyaXB0XzEiLCJjdXJyRGF0YUxheWVyTmFtZSIsInByb3BzIiwiZ3RtSWQiLCJndG1TY3JpcHRVcmwiLCJkYXRhTGF5ZXJOYW1lIiwiYXV0aCIsInByZXZpZXciLCJkYXRhTGF5ZXIiLCJub25jZSIsImd0bUxheWVyIiwiZ3RtQXV0aCIsImd0bVByZXZpZXciLCJ1c2VFZmZlY3QiLCJwZXJmb3JtYW5jZSIsIm1hcmsiLCJkZXRhaWwiLCJmZWF0dXJlIiwianN4cyIsIkZyYWdtZW50IiwiY2hpbGRyZW4iLCJqc3giLCJkZWZhdWx0IiwiaWQiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsIkpTT04iLCJzdHJpbmdpZnkiLCJzcmMiLCJkYXRhIiwid2luZG93IiwicHVzaCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/script.js":
/*!**********************************************!*\
  !*** ./node_modules/next/dist/api/script.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport default from dynamic */ _client_script__WEBPACK_IMPORTED_MODULE_0___default.a)\n/* harmony export */ });\n/* harmony import */ var _client_script__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/script */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\");\n/* harmony import */ var _client_script__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_script__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_script__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_script__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=script.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL3NjcmlwdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDVjs7QUFFakMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcQkVUQVxcQkVUQS1GUk9OVEVORFxcbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXHNjcmlwdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSAnLi4vY2xpZW50L3NjcmlwdCc7XG5leHBvcnQgKiBmcm9tICcuLi9jbGllbnQvc2NyaXB0JztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c2NyaXB0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/script.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(app-pages-browser)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@next/third-parties/dist/google/ga.js */ \"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/ga.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@next/third-parties/dist/google/gtm.js */ \"(app-pages-browser)/./node_modules/@next/third-parties/dist/google/gtm.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js */ \"(app-pages-browser)/./node_modules/@next/third-parties/dist/ThirdPartyScriptEmbed.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(app-pages-browser)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    cancelIdleCallback: function() {\n        return cancelIdleCallback;\n    },\n    requestIdleCallback: function() {\n        return requestIdleCallback;\n    }\n});\nconst requestIdleCallback = typeof self !== 'undefined' && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return self.setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nconst cancelIdleCallback = typeof self !== 'undefined' && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    handleClientScriptLoad: function() {\n        return handleClientScriptLoad;\n    },\n    initScriptLoader: function() {\n        return initScriptLoader;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _reactdom = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _setattributesfromprops = __webpack_require__(/*! ./set-attributes-from-props */ \"(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst insertStylesheets = (stylesheets)=>{\n    // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n    //\n    // Using ReactDOM.preinit to feature detect appDir and inject styles\n    // Stylesheets might have already been loaded if initialized with Script component\n    // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n    // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n    if (_reactdom.default.preinit) {\n        stylesheets.forEach((stylesheet)=>{\n            _reactdom.default.preinit(stylesheet, {\n                as: 'style'\n            });\n        });\n        return;\n    }\n    // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n    //\n    // We use this function to load styles when appdir is not detected\n    // TODO: Use React float APIs to load styles once available for pages dir\n    if (true) {\n        let head = document.head;\n        stylesheets.forEach((stylesheet)=>{\n            let link = document.createElement('link');\n            link.type = 'text/css';\n            link.rel = 'stylesheet';\n            link.href = stylesheet;\n            head.appendChild(link);\n        });\n    }\n};\nconst loadScript = (props)=>{\n    const { src, id, onLoad = ()=>{}, onReady = null, dangerouslySetInnerHTML, children = '', strategy = 'afterInteractive', onError, stylesheets } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n        // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    /** Execute after the script first loaded */ const afterLoad = ()=>{\n        // Run onReady for the first time after load event\n        if (onReady) {\n            onReady();\n        }\n        // add cacheKey to LoadCache when load successfully\n        LoadCache.add(cacheKey);\n    };\n    const el = document.createElement('script');\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener('load', function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n            afterLoad();\n        });\n        el.addEventListener('error', function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (dangerouslySetInnerHTML) {\n        // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n        el.innerHTML = dangerouslySetInnerHTML.__html || '';\n        afterLoad();\n    } else if (children) {\n        el.textContent = typeof children === 'string' ? children : Array.isArray(children) ? children.join('') : '';\n        afterLoad();\n    } else if (src) {\n        el.src = src;\n        // do not add cacheKey into LoadCache for remote script here\n        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n        ScriptCache.set(src, loadPromise);\n    }\n    (0, _setattributesfromprops.setAttributesFromProps)(el, props);\n    if (strategy === 'worker') {\n        el.setAttribute('type', 'text/partytown');\n    }\n    el.setAttribute('data-nscript', strategy);\n    // Load styles associated with this script\n    if (stylesheets) {\n        insertStylesheets(stylesheets);\n    }\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy = 'afterInteractive' } = props;\n    if (strategy === 'lazyOnload') {\n        window.addEventListener('load', ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    } else {\n        loadScript(props);\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === 'complete') {\n        (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n    } else {\n        window.addEventListener('load', ()=>{\n            (0, _requestidlecallback.requestIdleCallback)(()=>loadScript(props));\n        });\n    }\n}\nfunction addBeforeInteractiveToCache() {\n    const scripts = [\n        ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n        ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]')\n    ];\n    scripts.forEach((script)=>{\n        const cacheKey = script.id || script.getAttribute('src');\n        LoadCache.add(cacheKey);\n    });\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n    addBeforeInteractiveToCache();\n}\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */ function Script(props) {\n    const { id, src = '', onLoad = ()=>{}, onReady = null, strategy = 'afterInteractive', onError, stylesheets, ...restProps } = props;\n    // Context is available only during SSR\n    let { updateScripts, scripts, getIsSsr, appDir, nonce } = (0, _react.useContext)(_headmanagercontextsharedruntime.HeadManagerContext);\n    // if a nonce is explicitly passed to the script tag, favor that over the automatic handling\n    nonce = restProps.nonce || nonce;\n    /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */ const hasOnReadyEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        const cacheKey = id || src;\n        if (!hasOnReadyEffectCalled.current) {\n            // Run onReady if script has loaded before but component is re-mounted\n            if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n                onReady();\n            }\n            hasOnReadyEffectCalled.current = true;\n        }\n    }, [\n        onReady,\n        id,\n        src\n    ]);\n    const hasLoadScriptEffectCalled = (0, _react.useRef)(false);\n    (0, _react.useEffect)(()=>{\n        if (!hasLoadScriptEffectCalled.current) {\n            if (strategy === 'afterInteractive') {\n                loadScript(props);\n            } else if (strategy === 'lazyOnload') {\n                loadLazyScript(props);\n            }\n            hasLoadScriptEffectCalled.current = true;\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === 'beforeInteractive' || strategy === 'worker') {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                {\n                    id,\n                    src,\n                    onLoad,\n                    onReady,\n                    onError,\n                    ...restProps,\n                    nonce\n                }\n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript({\n                ...props,\n                nonce\n            });\n        }\n    }\n    // For the app directory, we need React Float to preload these scripts.\n    if (appDir) {\n        // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n        // For other strategies injecting here ensures correct stylesheet order\n        // ReactDOM.preinit handles loading the styles in the correct order,\n        // also ensures the stylesheet is loaded only once and in a consistent manner\n        //\n        // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n        // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n        // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n        // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n        if (stylesheets) {\n            stylesheets.forEach((styleSrc)=>{\n                _reactdom.default.preinit(styleSrc, {\n                    as: 'style'\n                });\n            });\n        }\n        // Before interactive scripts need to be loaded by Next.js' runtime instead\n        // of native <script> tags, because they no longer have `defer`.\n        if (strategy === 'beforeInteractive') {\n            if (!src) {\n                // For inlined scripts, we put the content in `children`.\n                if (restProps.dangerouslySetInnerHTML) {\n                    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n                    restProps.children = restProps.dangerouslySetInnerHTML.__html;\n                    delete restProps.dangerouslySetInnerHTML;\n                }\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            0,\n                            {\n                                ...restProps,\n                                id\n                            }\n                        ]) + \")\"\n                    }\n                });\n            } else {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: 'script',\n                    integrity: restProps.integrity,\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                } : {\n                    as: 'script',\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                });\n                return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: \"(self.__next_s=self.__next_s||[]).push(\" + JSON.stringify([\n                            src,\n                            {\n                                ...restProps,\n                                id\n                            }\n                        ]) + \")\"\n                    }\n                });\n            }\n        } else if (strategy === 'afterInteractive') {\n            if (src) {\n                // @ts-ignore\n                _reactdom.default.preload(src, restProps.integrity ? {\n                    as: 'script',\n                    integrity: restProps.integrity,\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                } : {\n                    as: 'script',\n                    nonce,\n                    crossOrigin: restProps.crossOrigin\n                });\n            }\n        }\n    }\n    return null;\n}\n_c = Script;\nObject.defineProperty(Script, '__nextScript', {\n    value: true\n});\nconst _default = Script;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=script.js.map\nvar _c;\n$RefreshReg$(_c, \"Script\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/script.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/client/set-attributes-from-props.js ***!
  \********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"setAttributesFromProps\", ({\n    enumerable: true,\n    get: function() {\n        return setAttributesFromProps;\n    }\n}));\nconst DOMAttributeNames = {\n    acceptCharset: 'accept-charset',\n    className: 'class',\n    htmlFor: 'for',\n    httpEquiv: 'http-equiv',\n    noModule: 'noModule'\n};\nconst ignoreProps = [\n    'onLoad',\n    'onReady',\n    'dangerouslySetInnerHTML',\n    'children',\n    'onError',\n    'strategy',\n    'stylesheets'\n];\nfunction isBooleanScriptAttribute(attr) {\n    return [\n        'async',\n        'defer',\n        'noModule'\n    ].includes(attr);\n}\nfunction setAttributesFromProps(el, props) {\n    for (const [p, value] of Object.entries(props)){\n        if (!props.hasOwnProperty(p)) continue;\n        if (ignoreProps.includes(p)) continue;\n        // we don't render undefined props to the DOM\n        if (value === undefined) {\n            continue;\n        }\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n            // Correctly assign boolean script attributes\n            // https://github.com/vercel/next.js/pull/20748\n            ;\n            el[attr] = !!value;\n        } else {\n            el.setAttribute(attr, String(value));\n        }\n        // Remove falsy non-zero boolean attributes so they are correctly interpreted\n        // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n        if (value === false || el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr) && (!value || value === 'false')) {\n            // Call setAttribute before, as we need to set and unset the attribute to override force async:\n            // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n            el.setAttribute(attr, '');\n            el.removeAttribute(attr);\n        }\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=set-attributes-from-props.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/set-attributes-from-props.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={432:(e,r,t)=>{var n=t(887);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},887:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(432);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7QUFDYixLQUFxQztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWCwrQ0FBK0MsNkJBQTZCO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsZ0JBQWdCLGdEQUFnRDtBQUNoRSxnQkFBZ0IsYUFBYTtBQUM3QjtBQUNBO0FBQ0EsZ0NBQWdDLGtDQUFrQyxPQUFPO0FBQ3pFO0FBQ0EsZ0dBQWdHLFNBQVMsVUFBVSxzRkFBc0YsYUFBYSxVQUFVLFVBQVU7QUFDMU87QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQU8sQ0FBQyxzR0FBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxnQkFBZ0I7QUFDcEIsSUFBSSxjQUFjO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxCRVRBXFxCRVRBLUZST05URU5EXFxuZXh0XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcY2pzXFxyZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBSZWFjdFxuICogcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzXG4gKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5cInVzZSBzdHJpY3RcIjtcblwicHJvZHVjdGlvblwiICE9PSBwcm9jZXNzLmVudi5OT0RFX0VOViAmJlxuICAoZnVuY3Rpb24gKCkge1xuICAgIGZ1bmN0aW9uIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKSB7XG4gICAgICBpZiAobnVsbCA9PSB0eXBlKSByZXR1cm4gbnVsbDtcbiAgICAgIGlmIChcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiB0eXBlKVxuICAgICAgICByZXR1cm4gdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRVxuICAgICAgICAgID8gbnVsbFxuICAgICAgICAgIDogdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgbnVsbDtcbiAgICAgIGlmIChcInN0cmluZ1wiID09PSB0eXBlb2YgdHlwZSkgcmV0dXJuIHR5cGU7XG4gICAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgICAgY2FzZSBSRUFDVF9GUkFHTUVOVF9UWVBFOlxuICAgICAgICAgIHJldHVybiBcIkZyYWdtZW50XCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfUFJPRklMRVJfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJQcm9maWxlclwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NUUklDVF9NT0RFX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3RyaWN0TW9kZVwiO1xuICAgICAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3VzcGVuc2VcIjtcbiAgICAgICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEU6XG4gICAgICAgICAgcmV0dXJuIFwiU3VzcGVuc2VMaXN0XCI7XG4gICAgICAgIGNhc2UgUkVBQ1RfQUNUSVZJVFlfVFlQRTpcbiAgICAgICAgICByZXR1cm4gXCJBY3Rpdml0eVwiO1xuICAgICAgfVxuICAgICAgaWYgKFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlKVxuICAgICAgICBzd2l0Y2ggKFxuICAgICAgICAgIChcIm51bWJlclwiID09PSB0eXBlb2YgdHlwZS50YWcgJiZcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiUmVjZWl2ZWQgYW4gdW5leHBlY3RlZCBvYmplY3QgaW4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKCkuIFRoaXMgaXMgbGlrZWx5IGEgYnVnIGluIFJlYWN0LiBQbGVhc2UgZmlsZSBhbiBpc3N1ZS5cIlxuICAgICAgICAgICAgKSxcbiAgICAgICAgICB0eXBlLiQkdHlwZW9mKVxuICAgICAgICApIHtcbiAgICAgICAgICBjYXNlIFJFQUNUX1BPUlRBTF9UWVBFOlxuICAgICAgICAgICAgcmV0dXJuIFwiUG9ydGFsXCI7XG4gICAgICAgICAgY2FzZSBSRUFDVF9DT05URVhUX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gdHlwZS5kaXNwbGF5TmFtZSB8fCBcIkNvbnRleHRcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0NPTlNVTUVSX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKHR5cGUuX2NvbnRleHQuZGlzcGxheU5hbWUgfHwgXCJDb250ZXh0XCIpICsgXCIuQ29uc3VtZXJcIjtcbiAgICAgICAgICBjYXNlIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEU6XG4gICAgICAgICAgICB2YXIgaW5uZXJUeXBlID0gdHlwZS5yZW5kZXI7XG4gICAgICAgICAgICB0eXBlID0gdHlwZS5kaXNwbGF5TmFtZTtcbiAgICAgICAgICAgIHR5cGUgfHxcbiAgICAgICAgICAgICAgKCh0eXBlID0gaW5uZXJUeXBlLmRpc3BsYXlOYW1lIHx8IGlubmVyVHlwZS5uYW1lIHx8IFwiXCIpLFxuICAgICAgICAgICAgICAodHlwZSA9IFwiXCIgIT09IHR5cGUgPyBcIkZvcndhcmRSZWYoXCIgKyB0eXBlICsgXCIpXCIgOiBcIkZvcndhcmRSZWZcIikpO1xuICAgICAgICAgICAgcmV0dXJuIHR5cGU7XG4gICAgICAgICAgY2FzZSBSRUFDVF9NRU1PX1RZUEU6XG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAoaW5uZXJUeXBlID0gdHlwZS5kaXNwbGF5TmFtZSB8fCBudWxsKSxcbiAgICAgICAgICAgICAgbnVsbCAhPT0gaW5uZXJUeXBlXG4gICAgICAgICAgICAgICAgPyBpbm5lclR5cGVcbiAgICAgICAgICAgICAgICA6IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8IFwiTWVtb1wiXG4gICAgICAgICAgICApO1xuICAgICAgICAgIGNhc2UgUkVBQ1RfTEFaWV9UWVBFOlxuICAgICAgICAgICAgaW5uZXJUeXBlID0gdHlwZS5fcGF5bG9hZDtcbiAgICAgICAgICAgIHR5cGUgPSB0eXBlLl9pbml0O1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgcmV0dXJuIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKGlubmVyVHlwZSkpO1xuICAgICAgICAgICAgfSBjYXRjaCAoeCkge31cbiAgICAgICAgfVxuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHRlc3RTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgcmV0dXJuIFwiXCIgKyB2YWx1ZTtcbiAgICB9XG4gICAgZnVuY3Rpb24gY2hlY2tLZXlTdHJpbmdDb2VyY2lvbih2YWx1ZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCA9ICExO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQgPSAhMDtcbiAgICAgIH1cbiAgICAgIGlmIChKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQpIHtcbiAgICAgICAgSlNDb21waWxlcl9pbmxpbmVfcmVzdWx0ID0gY29uc29sZTtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfdGVtcF9jb25zdCA9IEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdC5lcnJvcjtcbiAgICAgICAgdmFyIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMCA9XG4gICAgICAgICAgKFwiZnVuY3Rpb25cIiA9PT0gdHlwZW9mIFN5bWJvbCAmJlxuICAgICAgICAgICAgU3ltYm9sLnRvU3RyaW5nVGFnICYmXG4gICAgICAgICAgICB2YWx1ZVtTeW1ib2wudG9TdHJpbmdUYWddKSB8fFxuICAgICAgICAgIHZhbHVlLmNvbnN0cnVjdG9yLm5hbWUgfHxcbiAgICAgICAgICBcIk9iamVjdFwiO1xuICAgICAgICBKU0NvbXBpbGVyX3RlbXBfY29uc3QuY2FsbChcbiAgICAgICAgICBKU0NvbXBpbGVyX2lubGluZV9yZXN1bHQsXG4gICAgICAgICAgXCJUaGUgcHJvdmlkZWQga2V5IGlzIGFuIHVuc3VwcG9ydGVkIHR5cGUgJXMuIFRoaXMgdmFsdWUgbXVzdCBiZSBjb2VyY2VkIHRvIGEgc3RyaW5nIGJlZm9yZSB1c2luZyBpdCBoZXJlLlwiLFxuICAgICAgICAgIEpTQ29tcGlsZXJfaW5saW5lX3Jlc3VsdCRqc2NvbXAkMFxuICAgICAgICApO1xuICAgICAgICByZXR1cm4gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0VGFza05hbWUodHlwZSkge1xuICAgICAgaWYgKHR5cGUgPT09IFJFQUNUX0ZSQUdNRU5UX1RZUEUpIHJldHVybiBcIjw+XCI7XG4gICAgICBpZiAoXG4gICAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiB0eXBlICYmXG4gICAgICAgIG51bGwgIT09IHR5cGUgJiZcbiAgICAgICAgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfTEFaWV9UWVBFXG4gICAgICApXG4gICAgICAgIHJldHVybiBcIjwuLi4+XCI7XG4gICAgICB0cnkge1xuICAgICAgICB2YXIgbmFtZSA9IGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlKTtcbiAgICAgICAgcmV0dXJuIG5hbWUgPyBcIjxcIiArIG5hbWUgKyBcIj5cIiA6IFwiPC4uLj5cIjtcbiAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgcmV0dXJuIFwiPC4uLj5cIjtcbiAgICAgIH1cbiAgICB9XG4gICAgZnVuY3Rpb24gZ2V0T3duZXIoKSB7XG4gICAgICB2YXIgZGlzcGF0Y2hlciA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLkE7XG4gICAgICByZXR1cm4gbnVsbCA9PT0gZGlzcGF0Y2hlciA/IG51bGwgOiBkaXNwYXRjaGVyLmdldE93bmVyKCk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIFVua25vd25Pd25lcigpIHtcbiAgICAgIHJldHVybiBFcnJvcihcInJlYWN0LXN0YWNrLXRvcC1mcmFtZVwiKTtcbiAgICB9XG4gICAgZnVuY3Rpb24gaGFzVmFsaWRLZXkoY29uZmlnKSB7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIFwia2V5XCIpKSB7XG4gICAgICAgIHZhciBnZXR0ZXIgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKGNvbmZpZywgXCJrZXlcIikuZ2V0O1xuICAgICAgICBpZiAoZ2V0dGVyICYmIGdldHRlci5pc1JlYWN0V2FybmluZykgcmV0dXJuICExO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHZvaWQgMCAhPT0gY29uZmlnLmtleTtcbiAgICB9XG4gICAgZnVuY3Rpb24gZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIocHJvcHMsIGRpc3BsYXlOYW1lKSB7XG4gICAgICBmdW5jdGlvbiB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkoKSB7XG4gICAgICAgIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duIHx8XG4gICAgICAgICAgKChzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biA9ICEwKSxcbiAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgXCIlczogYGtleWAgaXMgbm90IGEgcHJvcC4gVHJ5aW5nIHRvIGFjY2VzcyBpdCB3aWxsIHJlc3VsdCBpbiBgdW5kZWZpbmVkYCBiZWluZyByZXR1cm5lZC4gSWYgeW91IG5lZWQgdG8gYWNjZXNzIHRoZSBzYW1lIHZhbHVlIHdpdGhpbiB0aGUgY2hpbGQgY29tcG9uZW50LCB5b3Ugc2hvdWxkIHBhc3MgaXQgYXMgYSBkaWZmZXJlbnQgcHJvcC4gKGh0dHBzOi8vcmVhY3QuZGV2L2xpbmsvc3BlY2lhbC1wcm9wcylcIixcbiAgICAgICAgICAgIGRpc3BsYXlOYW1lXG4gICAgICAgICAgKSk7XG4gICAgICB9XG4gICAgICB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkuaXNSZWFjdFdhcm5pbmcgPSAhMDtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShwcm9wcywgXCJrZXlcIiwge1xuICAgICAgICBnZXQ6IHdhcm5BYm91dEFjY2Vzc2luZ0tleSxcbiAgICAgICAgY29uZmlndXJhYmxlOiAhMFxuICAgICAgfSk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGVsZW1lbnRSZWZHZXR0ZXJXaXRoRGVwcmVjYXRpb25XYXJuaW5nKCkge1xuICAgICAgdmFyIGNvbXBvbmVudE5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUodGhpcy50eXBlKTtcbiAgICAgIGRpZFdhcm5BYm91dEVsZW1lbnRSZWZbY29tcG9uZW50TmFtZV0gfHxcbiAgICAgICAgKChkaWRXYXJuQWJvdXRFbGVtZW50UmVmW2NvbXBvbmVudE5hbWVdID0gITApLFxuICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgIFwiQWNjZXNzaW5nIGVsZW1lbnQucmVmIHdhcyByZW1vdmVkIGluIFJlYWN0IDE5LiByZWYgaXMgbm93IGEgcmVndWxhciBwcm9wLiBJdCB3aWxsIGJlIHJlbW92ZWQgZnJvbSB0aGUgSlNYIEVsZW1lbnQgdHlwZSBpbiBhIGZ1dHVyZSByZWxlYXNlLlwiXG4gICAgICAgICkpO1xuICAgICAgY29tcG9uZW50TmFtZSA9IHRoaXMucHJvcHMucmVmO1xuICAgICAgcmV0dXJuIHZvaWQgMCAhPT0gY29tcG9uZW50TmFtZSA/IGNvbXBvbmVudE5hbWUgOiBudWxsO1xuICAgIH1cbiAgICBmdW5jdGlvbiBSZWFjdEVsZW1lbnQoXG4gICAgICB0eXBlLFxuICAgICAga2V5LFxuICAgICAgc2VsZixcbiAgICAgIHNvdXJjZSxcbiAgICAgIG93bmVyLFxuICAgICAgcHJvcHMsXG4gICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgZGVidWdUYXNrXG4gICAgKSB7XG4gICAgICBzZWxmID0gcHJvcHMucmVmO1xuICAgICAgdHlwZSA9IHtcbiAgICAgICAgJCR0eXBlb2Y6IFJFQUNUX0VMRU1FTlRfVFlQRSxcbiAgICAgICAgdHlwZTogdHlwZSxcbiAgICAgICAga2V5OiBrZXksXG4gICAgICAgIHByb3BzOiBwcm9wcyxcbiAgICAgICAgX293bmVyOiBvd25lclxuICAgICAgfTtcbiAgICAgIG51bGwgIT09ICh2b2lkIDAgIT09IHNlbGYgPyBzZWxmIDogbnVsbClcbiAgICAgICAgPyBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJyZWZcIiwge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogITEsXG4gICAgICAgICAgICBnZXQ6IGVsZW1lbnRSZWZHZXR0ZXJXaXRoRGVwcmVjYXRpb25XYXJuaW5nXG4gICAgICAgICAgfSlcbiAgICAgICAgOiBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJyZWZcIiwgeyBlbnVtZXJhYmxlOiAhMSwgdmFsdWU6IG51bGwgfSk7XG4gICAgICB0eXBlLl9zdG9yZSA9IHt9O1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHR5cGUuX3N0b3JlLCBcInZhbGlkYXRlZFwiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiAwXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z0luZm9cIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogbnVsbFxuICAgICAgfSk7XG4gICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkodHlwZSwgXCJfZGVidWdTdGFja1wiLCB7XG4gICAgICAgIGNvbmZpZ3VyYWJsZTogITEsXG4gICAgICAgIGVudW1lcmFibGU6ICExLFxuICAgICAgICB3cml0YWJsZTogITAsXG4gICAgICAgIHZhbHVlOiBkZWJ1Z1N0YWNrXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0eXBlLCBcIl9kZWJ1Z1Rhc2tcIiwge1xuICAgICAgICBjb25maWd1cmFibGU6ICExLFxuICAgICAgICBlbnVtZXJhYmxlOiAhMSxcbiAgICAgICAgd3JpdGFibGU6ICEwLFxuICAgICAgICB2YWx1ZTogZGVidWdUYXNrXG4gICAgICB9KTtcbiAgICAgIE9iamVjdC5mcmVlemUgJiYgKE9iamVjdC5mcmVlemUodHlwZS5wcm9wcyksIE9iamVjdC5mcmVlemUodHlwZSkpO1xuICAgICAgcmV0dXJuIHR5cGU7XG4gICAgfVxuICAgIGZ1bmN0aW9uIGpzeERFVkltcGwoXG4gICAgICB0eXBlLFxuICAgICAgY29uZmlnLFxuICAgICAgbWF5YmVLZXksXG4gICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgc291cmNlLFxuICAgICAgc2VsZixcbiAgICAgIGRlYnVnU3RhY2ssXG4gICAgICBkZWJ1Z1Rhc2tcbiAgICApIHtcbiAgICAgIHZhciBjaGlsZHJlbiA9IGNvbmZpZy5jaGlsZHJlbjtcbiAgICAgIGlmICh2b2lkIDAgIT09IGNoaWxkcmVuKVxuICAgICAgICBpZiAoaXNTdGF0aWNDaGlsZHJlbilcbiAgICAgICAgICBpZiAoaXNBcnJheUltcGwoY2hpbGRyZW4pKSB7XG4gICAgICAgICAgICBmb3IgKFxuICAgICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuID0gMDtcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbiA8IGNoaWxkcmVuLmxlbmd0aDtcbiAgICAgICAgICAgICAgaXNTdGF0aWNDaGlsZHJlbisrXG4gICAgICAgICAgICApXG4gICAgICAgICAgICAgIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuW2lzU3RhdGljQ2hpbGRyZW5dKTtcbiAgICAgICAgICAgIE9iamVjdC5mcmVlemUgJiYgT2JqZWN0LmZyZWV6ZShjaGlsZHJlbik7XG4gICAgICAgICAgfSBlbHNlXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICBcIlJlYWN0LmpzeDogU3RhdGljIGNoaWxkcmVuIHNob3VsZCBhbHdheXMgYmUgYW4gYXJyYXkuIFlvdSBhcmUgbGlrZWx5IGV4cGxpY2l0bHkgY2FsbGluZyBSZWFjdC5qc3hzIG9yIFJlYWN0LmpzeERFVi4gVXNlIHRoZSBCYWJlbCB0cmFuc2Zvcm0gaW5zdGVhZC5cIlxuICAgICAgICAgICAgKTtcbiAgICAgICAgZWxzZSB2YWxpZGF0ZUNoaWxkS2V5cyhjaGlsZHJlbik7XG4gICAgICBpZiAoaGFzT3duUHJvcGVydHkuY2FsbChjb25maWcsIFwia2V5XCIpKSB7XG4gICAgICAgIGNoaWxkcmVuID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgICB2YXIga2V5cyA9IE9iamVjdC5rZXlzKGNvbmZpZykuZmlsdGVyKGZ1bmN0aW9uIChrKSB7XG4gICAgICAgICAgcmV0dXJuIFwia2V5XCIgIT09IGs7XG4gICAgICAgIH0pO1xuICAgICAgICBpc1N0YXRpY0NoaWxkcmVuID1cbiAgICAgICAgICAwIDwga2V5cy5sZW5ndGhcbiAgICAgICAgICAgID8gXCJ7a2V5OiBzb21lS2V5LCBcIiArIGtleXMuam9pbihcIjogLi4uLCBcIikgKyBcIjogLi4ufVwiXG4gICAgICAgICAgICA6IFwie2tleTogc29tZUtleX1cIjtcbiAgICAgICAgZGlkV2FybkFib3V0S2V5U3ByZWFkW2NoaWxkcmVuICsgaXNTdGF0aWNDaGlsZHJlbl0gfHxcbiAgICAgICAgICAoKGtleXMgPVxuICAgICAgICAgICAgMCA8IGtleXMubGVuZ3RoID8gXCJ7XCIgKyBrZXlzLmpvaW4oXCI6IC4uLiwgXCIpICsgXCI6IC4uLn1cIiA6IFwie31cIiksXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgICdBIHByb3BzIG9iamVjdCBjb250YWluaW5nIGEgXCJrZXlcIiBwcm9wIGlzIGJlaW5nIHNwcmVhZCBpbnRvIEpTWDpcXG4gIGxldCBwcm9wcyA9ICVzO1xcbiAgPCVzIHsuLi5wcm9wc30gLz5cXG5SZWFjdCBrZXlzIG11c3QgYmUgcGFzc2VkIGRpcmVjdGx5IHRvIEpTWCB3aXRob3V0IHVzaW5nIHNwcmVhZDpcXG4gIGxldCBwcm9wcyA9ICVzO1xcbiAgPCVzIGtleT17c29tZUtleX0gey4uLnByb3BzfSAvPicsXG4gICAgICAgICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgICAgICAgY2hpbGRyZW4sXG4gICAgICAgICAgICBrZXlzLFxuICAgICAgICAgICAgY2hpbGRyZW5cbiAgICAgICAgICApLFxuICAgICAgICAgIChkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY2hpbGRyZW4gKyBpc1N0YXRpY0NoaWxkcmVuXSA9ICEwKSk7XG4gICAgICB9XG4gICAgICBjaGlsZHJlbiA9IG51bGw7XG4gICAgICB2b2lkIDAgIT09IG1heWJlS2V5ICYmXG4gICAgICAgIChjaGVja0tleVN0cmluZ0NvZXJjaW9uKG1heWJlS2V5KSwgKGNoaWxkcmVuID0gXCJcIiArIG1heWJlS2V5KSk7XG4gICAgICBoYXNWYWxpZEtleShjb25maWcpICYmXG4gICAgICAgIChjaGVja0tleVN0cmluZ0NvZXJjaW9uKGNvbmZpZy5rZXkpLCAoY2hpbGRyZW4gPSBcIlwiICsgY29uZmlnLmtleSkpO1xuICAgICAgaWYgKFwia2V5XCIgaW4gY29uZmlnKSB7XG4gICAgICAgIG1heWJlS2V5ID0ge307XG4gICAgICAgIGZvciAodmFyIHByb3BOYW1lIGluIGNvbmZpZylcbiAgICAgICAgICBcImtleVwiICE9PSBwcm9wTmFtZSAmJiAobWF5YmVLZXlbcHJvcE5hbWVdID0gY29uZmlnW3Byb3BOYW1lXSk7XG4gICAgICB9IGVsc2UgbWF5YmVLZXkgPSBjb25maWc7XG4gICAgICBjaGlsZHJlbiAmJlxuICAgICAgICBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihcbiAgICAgICAgICBtYXliZUtleSxcbiAgICAgICAgICBcImZ1bmN0aW9uXCIgPT09IHR5cGVvZiB0eXBlXG4gICAgICAgICAgICA/IHR5cGUuZGlzcGxheU5hbWUgfHwgdHlwZS5uYW1lIHx8IFwiVW5rbm93blwiXG4gICAgICAgICAgICA6IHR5cGVcbiAgICAgICAgKTtcbiAgICAgIHJldHVybiBSZWFjdEVsZW1lbnQoXG4gICAgICAgIHR5cGUsXG4gICAgICAgIGNoaWxkcmVuLFxuICAgICAgICBzZWxmLFxuICAgICAgICBzb3VyY2UsXG4gICAgICAgIGdldE93bmVyKCksXG4gICAgICAgIG1heWJlS2V5LFxuICAgICAgICBkZWJ1Z1N0YWNrLFxuICAgICAgICBkZWJ1Z1Rhc2tcbiAgICAgICk7XG4gICAgfVxuICAgIGZ1bmN0aW9uIHZhbGlkYXRlQ2hpbGRLZXlzKG5vZGUpIHtcbiAgICAgIFwib2JqZWN0XCIgPT09IHR5cGVvZiBub2RlICYmXG4gICAgICAgIG51bGwgIT09IG5vZGUgJiZcbiAgICAgICAgbm9kZS4kJHR5cGVvZiA9PT0gUkVBQ1RfRUxFTUVOVF9UWVBFICYmXG4gICAgICAgIG5vZGUuX3N0b3JlICYmXG4gICAgICAgIChub2RlLl9zdG9yZS52YWxpZGF0ZWQgPSAxKTtcbiAgICB9XG4gICAgdmFyIFJlYWN0ID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9yZWFjdFwiKSxcbiAgICAgIFJFQUNUX0VMRU1FTlRfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC50cmFuc2l0aW9uYWwuZWxlbWVudFwiKSxcbiAgICAgIFJFQUNUX1BPUlRBTF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnBvcnRhbFwiKSxcbiAgICAgIFJFQUNUX0ZSQUdNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuZnJhZ21lbnRcIiksXG4gICAgICBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN0cmljdF9tb2RlXCIpLFxuICAgICAgUkVBQ1RfUFJPRklMRVJfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5wcm9maWxlclwiKSxcbiAgICAgIFJFQUNUX0NPTlNVTUVSX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuY29uc3VtZXJcIiksXG4gICAgICBSRUFDVF9DT05URVhUX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuY29udGV4dFwiKSxcbiAgICAgIFJFQUNUX0ZPUldBUkRfUkVGX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QuZm9yd2FyZF9yZWZcIiksXG4gICAgICBSRUFDVF9TVVNQRU5TRV9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN1c3BlbnNlXCIpLFxuICAgICAgUkVBQ1RfU1VTUEVOU0VfTElTVF9UWVBFID0gU3ltYm9sLmZvcihcInJlYWN0LnN1c3BlbnNlX2xpc3RcIiksXG4gICAgICBSRUFDVF9NRU1PX1RZUEUgPSBTeW1ib2wuZm9yKFwicmVhY3QubWVtb1wiKSxcbiAgICAgIFJFQUNUX0xBWllfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5sYXp5XCIpLFxuICAgICAgUkVBQ1RfQUNUSVZJVFlfVFlQRSA9IFN5bWJvbC5mb3IoXCJyZWFjdC5hY3Rpdml0eVwiKSxcbiAgICAgIFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UgPSBTeW1ib2wuZm9yKFwicmVhY3QuY2xpZW50LnJlZmVyZW5jZVwiKSxcbiAgICAgIFJlYWN0U2hhcmVkSW50ZXJuYWxzID1cbiAgICAgICAgUmVhY3QuX19DTElFTlRfSU5URVJOQUxTX0RPX05PVF9VU0VfT1JfV0FSTl9VU0VSU19USEVZX0NBTk5PVF9VUEdSQURFLFxuICAgICAgaGFzT3duUHJvcGVydHkgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LFxuICAgICAgaXNBcnJheUltcGwgPSBBcnJheS5pc0FycmF5LFxuICAgICAgY3JlYXRlVGFzayA9IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA/IGNvbnNvbGUuY3JlYXRlVGFza1xuICAgICAgICA6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH07XG4gICAgUmVhY3QgPSB7XG4gICAgICByZWFjdF9zdGFja19ib3R0b21fZnJhbWU6IGZ1bmN0aW9uIChjYWxsU3RhY2tGb3JFcnJvcikge1xuICAgICAgICByZXR1cm4gY2FsbFN0YWNrRm9yRXJyb3IoKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIHZhciBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bjtcbiAgICB2YXIgZGlkV2FybkFib3V0RWxlbWVudFJlZiA9IHt9O1xuICAgIHZhciB1bmtub3duT3duZXJEZWJ1Z1N0YWNrID0gUmVhY3QucmVhY3Rfc3RhY2tfYm90dG9tX2ZyYW1lLmJpbmQoXG4gICAgICBSZWFjdCxcbiAgICAgIFVua25vd25Pd25lclxuICAgICkoKTtcbiAgICB2YXIgdW5rbm93bk93bmVyRGVidWdUYXNrID0gY3JlYXRlVGFzayhnZXRUYXNrTmFtZShVbmtub3duT3duZXIpKTtcbiAgICB2YXIgZGlkV2FybkFib3V0S2V5U3ByZWFkID0ge307XG4gICAgZXhwb3J0cy5GcmFnbWVudCA9IFJFQUNUX0ZSQUdNRU5UX1RZUEU7XG4gICAgZXhwb3J0cy5qc3hERVYgPSBmdW5jdGlvbiAoXG4gICAgICB0eXBlLFxuICAgICAgY29uZmlnLFxuICAgICAgbWF5YmVLZXksXG4gICAgICBpc1N0YXRpY0NoaWxkcmVuLFxuICAgICAgc291cmNlLFxuICAgICAgc2VsZlxuICAgICkge1xuICAgICAgdmFyIHRyYWNrQWN0dWFsT3duZXIgPVxuICAgICAgICAxZTQgPiBSZWFjdFNoYXJlZEludGVybmFscy5yZWNlbnRseUNyZWF0ZWRPd25lclN0YWNrcysrO1xuICAgICAgcmV0dXJuIGpzeERFVkltcGwoXG4gICAgICAgIHR5cGUsXG4gICAgICAgIGNvbmZpZyxcbiAgICAgICAgbWF5YmVLZXksXG4gICAgICAgIGlzU3RhdGljQ2hpbGRyZW4sXG4gICAgICAgIHNvdXJjZSxcbiAgICAgICAgc2VsZixcbiAgICAgICAgdHJhY2tBY3R1YWxPd25lclxuICAgICAgICAgID8gRXJyb3IoXCJyZWFjdC1zdGFjay10b3AtZnJhbWVcIilcbiAgICAgICAgICA6IHVua25vd25Pd25lckRlYnVnU3RhY2ssXG4gICAgICAgIHRyYWNrQWN0dWFsT3duZXIgPyBjcmVhdGVUYXNrKGdldFRhc2tOYW1lKHR5cGUpKSA6IHVua25vd25Pd25lckRlYnVnVGFza1xuICAgICAgKTtcbiAgICB9O1xuICB9KSgpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcQkVUQVxcQkVUQS1GUk9OVEVORFxcbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist","arguments":[{"variable":"--font-geist-sans","subsets":["latin"]}],"variableName":"geistSans"} ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Geist', 'Geist Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_5cfdac\",\"variable\":\"__variable_5cfdac\"};\n    if(true) {\n      // 1755848897603\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiR2Vpc3RcIixcImFyZ3VtZW50c1wiOlt7XCJ2YXJpYWJsZVwiOlwiLS1mb250LWdlaXN0LXNhbnNcIixcInN1YnNldHNcIjpbXCJsYXRpblwiXX1dLFwidmFyaWFibGVOYW1lXCI6XCJnZWlzdFNhbnNcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyw4REFBOEQ7QUFDekYsT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQW1JLGNBQWMsc0RBQXNEO0FBQ3JPLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcQXN1c1xcRGVza3RvcFxcQkVUQVxcQkVUQS1GUk9OVEVORFxcbmV4dFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxmb250XFxnb29nbGVcXHRhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkdlaXN0XCIsXCJhcmd1bWVudHNcIjpbe1widmFyaWFibGVcIjpcIi0tZm9udC1nZWlzdC1zYW5zXCIsXCJzdWJzZXRzXCI6W1wibGF0aW5cIl19XSxcInZhcmlhYmxlTmFtZVwiOlwiZ2Vpc3RTYW5zXCJ9fGFwcC1wYWdlcy1icm93c2VyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidHZWlzdCcsICdHZWlzdCBGYWxsYmFjaydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV81Y2ZkYWNcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlXzVjZmRhY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzU1ODQ4ODk3NjAzXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkM6L1VzZXJzL0FzdXMvRGVza3RvcC9CRVRBL0JFVEEtRlJPTlRFTkQvbmV4dC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":
/*!*************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Geist_Mono","arguments":[{"variable":"--font-geist-mono","subsets":["latin"]}],"variableName":"geistMono"} ***!
  \*************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'Geist Mono', 'Geist Mono Fallback'\",\"fontStyle\":\"normal\"},\"className\":\"__className_9a8899\",\"variable\":\"__variable_9a8899\"};\n    if(true) {\n      // 1755848897604\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiR2Vpc3RfTW9ub1wiLFwiYXJndW1lbnRzXCI6W3tcInZhcmlhYmxlXCI6XCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFwic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImdlaXN0TW9ub1wifSIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQixTQUFTLHdFQUF3RTtBQUNuRyxPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBbUksY0FBYyxzREFBc0Q7QUFDck8sTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBc3VzXFxEZXNrdG9wXFxCRVRBXFxCRVRBLUZST05URU5EXFxuZXh0XFxub2RlX21vZHVsZXNcXG5leHRcXGZvbnRcXGdvb2dsZVxcdGFyZ2V0LmNzcz97XCJwYXRoXCI6XCJhcHBcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiR2Vpc3RfTW9ub1wiLFwiYXJndW1lbnRzXCI6W3tcInZhcmlhYmxlXCI6XCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFwic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImdlaXN0TW9ub1wifXxhcHAtcGFnZXMtYnJvd3NlciJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInR2Vpc3QgTW9ubycsICdHZWlzdCBNb25vIEZhbGxiYWNrJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lXzlhODg5OVwiLFwidmFyaWFibGVcIjpcIl9fdmFyaWFibGVfOWE4ODk5XCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTU4NDg4OTc2MDRcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiQzovVXNlcnMvQXN1cy9EZXNrdG9wL0JFVEEvQkVUQS1GUk9OVEVORC9uZXh0L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cga.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5Cgoogle%5C%5Cgtm.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5C%40next%5C%5Cthird-parties%5C%5Cdist%5C%5CThirdPartyScriptEmbed.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAsus%5C%5CDesktop%5C%5CBETA%5C%5CBETA-FRONTEND%5C%5Cnext%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);