/* 404 Not Found Page Styles */
.not-found {
  min-height: 100vh;
  background: linear-gradient(135deg, #fefaef 0%, #FBFBF9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  position: relative;
  overflow: hidden;
  font-family: 'Source Sans Pro', sans-serif;
}

.not-found-container {
  max-width: 800px;
  width: 100%;
  position: relative;
  z-index: 2;
}

.not-found-content {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0;
  padding: 3rem 2rem;
  box-shadow: 0px 4px 47.7px 0px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(212, 175, 55, 0.2);
  backdrop-filter: blur(10px);
}

/* Legal-themed icon */
.not-found-icon {
  margin-bottom: 2rem;
}

.not-found-scales {
  color: #d4af37;
  width: 120px;
  height: 120px;
  opacity: 0.8;
}

/* Error code */
.not-found-code {
  font-family: 'Battambang', sans-serif;
  font-size: clamp(4rem, 8vw, 8rem);
  font-weight: 700;
  color: #d4af37;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1;
}

/* Main heading */
.not-found-title {
  font-family: 'Battambang', sans-serif;
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

/* Description */
.not-found-description {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: clamp(1rem, 2vw, 1.2rem);
  color: #666;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Action buttons */
.not-found-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.not-found-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 1.5rem;
  border-radius: 0;
  font-family: 'Merriweather', serif;
  font-weight: 600;
  font-size: 0.95rem;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
}

.not-found-btn-primary {
  background: #d4af37;
  color: #1a1a1a;
}

.not-found-btn-primary:hover {
  background: #f4d03f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.not-found-btn-secondary {
  background: transparent;
  color: #666;
  border: 2px solid #ddd;
}

.not-found-btn-secondary:hover {
  background: #f8f8f8;
  border-color: #d4af37;
  color: #1a1a1a;
  transform: translateY(-2px);
}

/* Help section */
.not-found-help {
  margin-bottom: 2.5rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(212, 175, 55, 0.2);
}

.not-found-help-text {
  font-family: 'Source Sans Pro', sans-serif;
  color: #666;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.not-found-help-link {
  background: none;
  border: none;
  color: #d4af37;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: 0.95rem;
  transition: color 0.3s ease;
}

.not-found-help-link:hover {
  color: #b8941f;
}

/* Popular links */
.not-found-links {
  margin-top: 2rem;
}

.not-found-links-title {
  font-family: 'Battambang', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 1rem;
}

.not-found-links-grid {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.not-found-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
  border-radius: 0;
  text-decoration: none;
  color: #1a1a1a;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.not-found-link:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: #d4af37;
  transform: translateY(-1px);
}

.not-found-link-icon {
  font-size: 1.1rem;
}

/* Background decoration */
.not-found-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.not-found-decoration-item {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(212, 175, 55, 0.1), rgba(212, 175, 55, 0.05));
  animation: float 6s ease-in-out infinite;
}

.not-found-decoration-item-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.not-found-decoration-item-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.not-found-decoration-item-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.1;
  }
}

/* Mobile responsiveness */
@media (max-width: 767px) {
  .not-found {
    padding: 1rem;
  }

  .not-found-content {
    padding: 2rem 1.5rem;
  }

  .not-found-actions {
    flex-direction: column;
    align-items: center;
  }

  .not-found-btn {
    width: 100%;
    max-width: 280px;
  }

  .not-found-links-grid {
    flex-direction: column;
    align-items: center;
  }

  .not-found-link {
    width: 100%;
    max-width: 200px;
  }

  .not-found-decoration-item {
    display: none;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .not-found-content {
    padding: 2.5rem 2rem;
  }
}
