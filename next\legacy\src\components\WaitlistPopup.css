.waitlist-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000000;
  display: none;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.waitlist-popup__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  
  -webkit-backdrop-filter: blur(8px);
}

.waitlist-popup__content {
  position: relative;
  background-color: #fdfbf4;
  border: 2px solid #966f33;
  max-width: 500px;
  width: 100%;
  max-height: fit-content;
  overflow-y: auto;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  touch-action: pan-y;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.waitlist-popup__close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: none;
  font-size: 32px;
  color: #966f33;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

.waitlist-popup__close:hover {
  color: #000;
  transform: scale(1.1);
}

.waitlist-popup__header {
  margin-bottom: 32px;
  text-align: center;
}

.waitlist-popup__title {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 32px;
  letter-spacing: -0.05em;
  line-height: 36px;
  color: black;
  margin: 0 0 16px 0;
}

.waitlist-popup__description {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: 18px;
  line-height: 24px;
  color: #966f33;
  margin: 0;
}

.waitlist-popup__form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.waitlist-popup__form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.waitlist-popup__label {
  font-family: 'Battambang', sans-serif;
  font-size: 16px;
  font-weight: 700;
  color: black;
  letter-spacing: -0.02em;
}

.waitlist-popup__input {
  padding: 16px 20px;
  border: 2px solid #e5cca4;
  background-color: #fdfbf4;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
  line-height: 20px;
  color: black;
  transition: all 0.3s ease;
  outline: none;
}

.waitlist-popup__input:focus {
  border-color: #966f33;
  box-shadow: 0 0 0 3px rgba(150, 111, 51, 0.1);
}

.waitlist-popup__input::placeholder {
  color: #966f33;
  opacity: 0.7;
}

.waitlist-popup__form-button {
  margin-top: 8px;
  display: flex;
  justify-content: center;
}

.waitlist-popup__form-button .button {
  width: 100%;
  max-width: 300px;
}

/* Success State */
.waitlist-popup__success {
  text-align: center;
  padding: 20px 0;
}

.waitlist-popup__success-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #966f33;
  color: white;
  font-size: 48px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px auto;
}

.waitlist-popup__success-title {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 28px;
  letter-spacing: -0.05em;
  line-height: 32px;
  color: black;
  margin: 0 0 16px 0;
}

.waitlist-popup__success-message {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: 18px;
  line-height: 24px;
  color: #966f33;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .waitlist-popup__content {
    padding: 30px 24px;
    margin: 20px;
    overflow-y: scroll;
  }

  .waitlist-popup__title {
    font-size: 28px;
    line-height: 32px;
  }

  .waitlist-popup__description {
    font-size: 16px;
    line-height: 22px;
  }

  .waitlist-popup__input {
    padding: 14px 16px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .waitlist-popup {
    padding: 10px;
  }

  .waitlist-popup__content {
    padding: 24px 20px;
    margin: 10px;
    overflow-y: scroll;
  }

  .waitlist-popup__title {
    font-size: 24px;
    line-height: 28px;
  }

  .waitlist-popup__close {
    top: 15px;
    right: 15px;
    font-size: 28px;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 20;
  }

  .waitlist-popup__close:hover,
  .waitlist-popup__close:active {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.05);
  }
}

/* Loading state for button */
.waitlist-popup__form-button .button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Animation classes for GSAP */
.waitlist-popup__content,
.waitlist-popup__title,
.waitlist-popup__description,
.waitlist-popup__form-group,
.waitlist-popup__form-button,
.waitlist-popup__success {
  will-change: transform, opacity;
}

/* Enhanced mobile touch interactions */
@media (hover: none) and (pointer: coarse) {
  .waitlist-popup__close:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: none;
  }

  .waitlist-popup__close:active {
    background: rgba(255, 255, 255, 1);
    transform: scale(0.95);
  }

  /* Ensure form inputs are touch-friendly */
  .waitlist-popup__input {
    min-height: 48px;
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Make buttons more touch-friendly */
  .waitlist-popup__form-button .button {
    min-height: 48px;
    padding: 16px 24px;
  }
}
