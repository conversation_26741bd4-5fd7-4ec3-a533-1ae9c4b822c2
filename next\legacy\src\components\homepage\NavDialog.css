/* --- Root styles for the dialog --- */
.nav-dialog {
  position: absolute;
  top: 100%;
  right: 0;
  transform: translateX(-50%);
  width: clamp(800px, 80vw, 1100px);
  background-color: #F5F0EB;
  border: 1px solid #e0d9d3;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
  
  margin-top: 10px;
  padding: 2rem 2.5rem;
  z-index: 1000;
  border-top: 3px solid #C0A062;
  cursor: default;
}

/* --- Title and Divider styles --- */
.dialog-title {
  font-family: 'Serif', 'Playfair Display';
  font-size: 1.8rem;
  font-weight: 500;
  color: #1A1A1A;
  margin: 0 0 0.5rem 0;
  text-align: center;
}

.dialog-divider {
  border: none;
  height: 1px;
  background-color: #e0d9d3;
  margin: 0.5rem 0 2rem 0;
}

/* --- Grid container --- */
.dialog-items-grid {
  display: grid;
  gap: 2.5rem;
}

/* --- Specific grid layouts --- */
.grid-features {
  grid-template-columns: repeat(2, 1fr);
}

.grid-testimonials {
  grid-template-columns: repeat(3, 1fr);
}

/* --- Individual item/card styles --- */
.dialog-item {
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #EAE3DC;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dialog-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

/* --- MODIFIED: Heading (h4) styles --- */
.dialog-item h4 {
  font-family: 'Serif', 'Playfair Display';
  font-size: 1.1rem;
  color: #1A1A1A;
  margin: 0 0 1rem 0; /* Increased bottom margin for spacing */
  position: relative; /* Crucial for positioning the pseudo-element */
  display: inline-block; /* Ensures the element width fits the content */
  padding-bottom: 8px; /* Add padding to create space for the underline */
}

/* --- NEW: Gold accent underline --- */
.dialog-item h4::after {
  content: ''; /* Required for pseudo-elements */
  position: absolute;
  bottom: 0; /* Position at the bottom of the h4 */
  left: 0; /* (100% - 70%) / 2, to center the underline */
  width: 70%; /* Set the width to 70% of the h4 element */
  height: 2px; /* Thickness of the underline */
  background-color: #C0A062; /* Gold accent color */
}

.dialog-item p {
  font-family: 'Sans-Serif', 'Lato';
  font-size: 0.9rem;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.dialog-item .profession {
  font-style: italic;
  font-size: 0.8rem;
  color: #777;
  margin-bottom: 1rem;
}

.dialog-item blockquote {
  font-family: 'Serif', 'Playfair Display';
  font-style: italic;
  font-size: 0.95rem;
  color: #333;
  margin: 0;
  padding-left: 1rem;
  border-left: 2px solid #C0A062;
  line-height: 1.5;
}