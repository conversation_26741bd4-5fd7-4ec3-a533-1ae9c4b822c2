
.footer {
  width: 100%;

  color: #26211D;
  border-top: 1px solid #DFDBD7;
}

/* Top: Brand + Navigation */
.footer__top {
  display: grid;
  grid-template-columns: 1fr;
}

@media (min-width: 768px) {
  .footer__top {
    grid-template-columns: 2fr 1fr;
  }
}

.footer__brand {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
}

@media (min-width: 768px) {
  .footer__brand {
    justify-content: flex-start;
    padding: 64px 48px;
    height: 442px;
  }
}

.footer__brand-group {
  display: flex;
  align-items: center;
  gap: clamp(16px, 2.5vw, 32px);
}

.footer__logo {
  width: clamp(48px, 12vw, 150px);
  height: auto;
  display: block;
}

.footer__nav {
  border-top:  1px solid #DFDBD7;
}

@media (min-width: 768px) {
  .footer__nav { border-top: none; border-left: 1px solid #DFDBD7; justify-content: space-evenly; display: flex; flex-direction: column;}
}

.footer__nav-link {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px 24px;
  border-top:  1px solid #DFDBD7;
  height: 300px;
  text-decoration: none;
  color: inherit;
  transition: background-color 0.2s ease-out;
}
.footer__nav-link:first-child { border-top: none; }
.footer__nav-link:hover { background-color: hsl(var(--accent) / 0.5); }

.footer__nav-id {
  min-width: 3ch;
  color: hsl(var(--muted-foreground));
  letter-spacing: 0.04em;
}

/* Bottom: 3 columns */
.footer__bottom {
  display: grid;
  grid-template-columns: 1fr;
  border-top: 1px solid #DFDBD7;;
}

@media (min-width: 768px) {
  .footer__bottom { grid-template-columns: repeat(3, 1fr); }
}

.footer__section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px 14px;
  height:283px;
  
  border-top:  1px solid #DFDBD7;
  
}

.footer__section:first-child { border-top: none; }



.footer__section-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer__dot {
  width: 8px; height: 8px; border-radius: 9999px;
  background-color: #9C9C9C;
  display: inline-block;
}

.footer__links {
  display: flex;
  flex-wrap: wrap;
  column-gap: 40px;
  row-gap: 8px;
}

.footer__link { color: inherit; text-decoration: none; }
.footer__link:hover { text-decoration: underline; }

.footer__meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.brand-heading {
    font-family: 'Playfair Display', serif;
    font-weight: 100;
    line-height: 0.95;
    letter-spacing: -0.01em;
    font-size: clamp(50px, 11vw, 250px);
  }

  .footer-title {
    font-family: 'Arial';
    font-weight: 600;
    color: #9C9C9C;
    font-size: clamp(17px, 1.2vw, 20px);
  }

  .footer-desc {
    font-family: 'Arial';
    font-weight: 400;
    font-size: clamp(16px, 1.6vw, 20px);
  }
  @media (min-width: 768px) {
  .footer__section {
    padding: 41px 41px;
    border-top: none;
    height: 283px;
    display: flex;
    justify-content: space-between;
    border-left:  1px solid #DFDBD7;;
  }
  .footer__section:first-child { border-left: none; }

  .footer__nav{
    height: 442px;
  }
  .footer__section{
   
    
  }
    
}
@media (max-width: 768px) {
  .footer__section {
    justify-content: space-between;

  }
  .footer__section:first-child { border-left: none; }
  .footer__nav-link{
    height: 100px;

  }
  .footer__nav{
    height: 300px;
  }
  .footer__section{
    height:166px;

  }
}