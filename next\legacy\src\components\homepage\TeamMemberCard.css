@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");

.team-member-card {
    width: 200px;
    height: 500px;
    border-radius: 100px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    overflow: hidden;
    margin: 0 10px;
    background-color: #FFF8E4;
    color: #5D4037;
    box-shadow: 0 8px 32px rgba(150, 111, 51, 0.2);
    will-change: transform, box-shadow;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}



.team-member-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #966f33 50%, transparent 100%);
    opacity: 0.3;
}

.team-member-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #966f33 50%, transparent 100%);
    opacity: 0.3;
}

.team-member-details {
    padding: 30px 15px 15px;
    text-align: center;
    height: 35%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.team-member-details h3 {
    margin: 0;
    margin-top: 0.5rem;
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    
    color: #5D4037;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.team-member-details p {
    margin: 6px 0 0;
    font-family: "Source Sans Pro", sans-serif;
    font-size: 0.9rem;
    color: #966f33;
    font-weight: 600;
    line-height: 1.3;
}

.team-member-image {
    width: 100%;
    height: 65%;
    position: relative;
    bottom: -15px;
}

.team-member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: grayscale(100%);
    transform: scale(1.1);
    transition: filter 0.3s ease;
}

.team-member-card:hover .team-member-image img {
    filter: grayscale(0%);
}

/* Y offset is now handled by framer-motion */

/* Responsive adjustments */
@media (max-width: 1200px) {
    .team-member-card {
        width: 180px;
        height: 450px;
        border-radius: 90px;
        margin: 0 8px;
    }
    
    .team-member-details h3 {
        font-size: 1.2rem;
    }
    
    .team-member-details p {
        font-size: 0.85rem;
    }
}

@media (max-width: 992px) {
    .team-member-card {
        width: 160px;
        height: 400px;
        border-radius: 80px;
        margin: 0 6px;
    }
    
    .team-member-details {
        padding: 25px 12px 12px;
    }
    
    .team-member-details h3 {
        font-size: 1.1rem;
    }
    
    .team-member-details p {
        font-size: 0.8rem;
    }
}

@media (max-width: 767px) {
    .team-member-card {
        width: 140px;
        height: 350px;
        border-radius: 70px;
        margin: 0 4px;
        flex-shrink: 0;
    }
    
    .team-member-details {
        padding: 20px 10px 10px;
    }
    
    .team-member-details h3 {
        font-size: 1rem;
        margin-top: 0.3rem;
    }
    
    .team-member-details p {
        font-size: 0.75rem;
        margin: 4px 0 0;
    }
}