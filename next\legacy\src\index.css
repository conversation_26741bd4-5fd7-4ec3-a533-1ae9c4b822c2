@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&family=Roboto+Flex:opsz,wght@8..144,100..1000&family=Spirax&display=swap');
/* --- Font Definitions --- */
/* Place this at the very top of your CSS file */

@font-face {
  font-family: 'Nyght Serif'; /* The name you will use in your CSS */
  src: url('/fonts/nyght-serif-regular.woff2') format('woff2'); /* Path to the file */
  font-weight: 400; /* or 'normal' */
  font-style: normal;
  font-display: swap; /* Crucial for performance! */
}

@font-face {
  font-family: 'Nyght Serif'; /* Same name, but for the bold version */
  src: url('/fonts/nyght/NyghtSerif-Bold.woff2') format('woff2');
  font-weight: 700; /* or 'bold' */
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Nyght Serif'; /* Same name, but for the bold version */
  src: url('/fonts/nyght/NyghtSerif-Light.woff2') format('woff2');
  font-weight: 300; /* or 'bold' */
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Nyght Serif'; /* Same name, but for the bold version */
  src: url('/fonts/nyght/NyghtSerif-LightItalic.woff2') format('woff2');
  font-weight: 300; /* or 'bold' */
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'Nyght Serif'; /* Same name, but for the bold version */
  src: url('/fonts/nyght/NyghtSerif-RegularItalic.woff2') format('woff2');
  font-weight: 400; /* or 'bold' */
  font-style: italic;
  font-display: swap;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}


body {
  overflow-x: hidden;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #F7F3ED;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
