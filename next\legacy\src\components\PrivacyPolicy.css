/* Import fonts matching homepage */
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Baskerville+Old+Face:wght@400&display=swap");

.privacy-policy {
  min-height: 100vh;
  
  background: linear-gradient(135deg, #FDFBF4 0%, #FFF8E4 50%, #FDFBF4 100%);
  overflow-x: hidden;
}

.privacy-policy__container {
 
  margin: 0 auto;
  padding: 0;
}

/* Hero Section */
.privacy-policy__hero {
  background: #FFF8E4;
  padding: 120px 0 80px 0;
  text-align: center;
  position: relative;
}

.privacy-policy__hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(150, 111, 51, 0.05) 0%, rgba(150, 111, 51, 0.02) 100%);
  pointer-events: none;
}

.privacy-policy__hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.privacy-policy__title {
  font-family: "Playfair Display", serif;
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 400;
  color: #966F33;
  margin: 0 0 2rem 0;
  letter-spacing: -0.02em;
  line-height: 1.1;
}
.privacy-policy .navbar {
  flex-direction: row !important;
  gap: 0 !important;


 
}

.privacy-policy .navbar__content {
  flex-direction: row !important;
  gap: 20px !important;
  justify-content: flex-end !important;
}

.privacy-policy .navbar__nav {
  display: none !important; /* Hide navigation links on mobile for cleaner look */
}

.privacy-policy .navbar__brand {
  font-size: 28px !important;
  text-align: left !important;
}
.privacy-policy__subtitle {
  font-family: "Roboto Flex", sans-serif;
  
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  color: #666;
  line-height: 1.6;
}

.privacy-policy__subtitle p {
  margin: 0.5rem 0;
}

/* Content Section */
.privacy-policy__content {
  background: #FDFBF4;
  padding: 80px 2rem;
}

.privacy-policy__intro {
  max-width: 900px;
  margin: 0 auto 4rem auto;
  text-align: center;
  padding: 2rem;
  background: rgba(150, 111, 51, 0.05);
  border-radius: 0;
  border-left: 4px solid #966F33;
}

.privacy-policy__intro p {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: clamp(1.1rem, 2.5vw, 1.3rem);
  color: #666;
  line-height: 1.7;
  margin: 0;
}

/* Sections */
.privacy-policy__sections {
  max-width: 900px;
  margin: 0 auto;
}

.privacy-policy__section {
  display: flex;
  gap: 3rem;
  margin-bottom: 1rem;
  padding: 2rem 0;
  border-bottom: 1px solid rgba(150, 111, 51, 0.1);
}

.privacy-policy__section:last-child {
  border-bottom: none;
}

.privacy-policy__section-number {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  background: #966F33;
  color: #FFF8E4;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 700;
  position: relative;
  top: 0.5rem;
}

.privacy-policy__section-content {
  flex: 1;
}

.privacy-policy__section-content h2 {
 font-family: "Playfair Display", serif;
  font-size: clamp(1.5rem, 3vw, 2rem);
  font-weight: 500;
  color: #966F33;
  margin: 0 0 1.5rem 0;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.privacy-policy__section-content h3 {
  font-family: 'Playfair Display', serif;
  font-size: clamp(1.2rem, 2.5vw, 1.5rem);
  font-weight: 500;
  color: #966F33;
  margin: 2rem 0 1rem 0;
  letter-spacing: -0.01em;
}

.privacy-policy__section-content p {
  font-family: "Roboto Flex", sans-serif;
  font-size: clamp(1rem, 2vw, 1.1rem);
  line-height: 1.7;
  color: #333;
  margin-bottom: 1.5rem;
}

.privacy-policy__section-content ul {
  margin: 1.5rem 0;
  padding-left: 0;
  list-style: none;
}

.privacy-policy__section-content li {
  font-family: "Roboto Flex", sans-serif;
  font-size: clamp(1rem, 2vw, 1.1rem);
  line-height: 1.7;
  color: #333;
  margin-bottom: 1rem;
  padding-left: 2rem;
  position: relative;
}

.privacy-policy__section-content li::before {
  content: '•';
  color: #966F33;
  font-weight: bold;
  position: absolute;
  left: 0.5rem;
  font-size: 1.2rem;
}

.privacy-policy__section-content a {
  color: #966F33;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border-bottom: 1px solid transparent;
}

.privacy-policy__section-content a:hover {
  color: #7a5a2a;
  border-bottom-color: #7a5a2a;
}

/* Contact Section */
.privacy-policy__contact {
  background: rgba(150, 111, 51, 0.05);
  padding: 2rem;
  border-radius: 0;
  margin-top: 2rem;
  border-left: 4px solid #966F33;
}

.privacy-policy__contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  font-family: "Roboto Flex", sans-serif;
  font-size: clamp(1rem, 2vw, 1.1rem);
  line-height: 1.7;
  color: #333;
}

.privacy-policy__contact-item:last-child {
  margin-bottom: 0;
}

.privacy-policy__contact-icon {
  font-size: 1.2rem;
  width: 2rem;
  text-align: center;
}

/* CTA Section */
.privacy-policy__cta {
  text-align: center;
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 1px solid rgba(150, 111, 51, 0.1);
}

.privacy-policy__back-button {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2.5rem;
  background: #966F33;
  color: #FFF8E4;
  text-decoration: none;
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  font-size: 1.1rem;
  letter-spacing: 0.02em;
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(150, 111, 51, 0.25);
}

.privacy-policy__back-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.privacy-policy__back-button:hover::before {
  left: 100%;
}

.privacy-policy__back-button:hover {
  background: #7a5a2a;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(150, 111, 51, 0.35);
}

.privacy-policy__back-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.privacy-policy__back-button:hover .privacy-policy__back-arrow {
  transform: translateX(4px);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .privacy-policy__hero {
    padding: 50px 0 60px 0;

  }
  .privacy-policy{
    margin-top:10vh;
  }

  .privacy-policy__hero-content {
    padding: 0 1.5rem;
  }

  .privacy-policy__content {
    padding: 60px 1.5rem;
  }

  .privacy-policy__intro {
    padding: 1.5rem;
    margin-bottom: 3rem;
  }

  .privacy-policy__section {
    flex-direction: column;
    gap: 1.5rem;
    
    padding: 1.5rem 0;
  }

  .privacy-policy__section-number {
    width: 60px;
    height: 60px;
    font-size: 1.2rem;
    align-self: flex-start;
  }

  .privacy-policy__contact {
    padding: 1.5rem;
  }

  .privacy-policy__contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    text-align: left;
  }

  .privacy-policy__contact-icon {
    width: auto;
  }

  .privacy-policy__cta {
    margin-top: 3rem;
    padding-top: 2rem;
  }

  .privacy-policy__back-button {
    padding: 0.875rem 2rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .privacy-policy__hero {
    padding: 80px 0 50px 0;
    justify-content: center;
    align-items: center;
    align-content: center;
    width: 100vw;
  }
  .navbar{
    width: 100vw;
  }

  .privacy-policy__hero-content {
    padding: 0 1rem;
  }

  .privacy-policy__content {
    padding: 50px 1rem;
  }

  .privacy-policy__intro {
    padding: 1rem;
    margin-bottom: 2.5rem;
  }

  .privacy-policy__section {
    margin-bottom: 2.5rem;
    padding: 1rem 0;
  }

  .privacy-policy__section-number {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }

  .privacy-policy__section-content li {
    padding-left: 1.5rem;
  }

  .privacy-policy__contact {
    padding: 1rem;
  }

  .privacy-policy__back-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    gap: 0.75rem;
  }
}